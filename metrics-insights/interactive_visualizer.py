import csv
import matplotlib.pyplot as plt
from collections import defaultdict
import tkinter as tk
from tkinter import ttk
import sys


class InteractiveMetricVisualizer:
    """
    Interactive metric visualizer with dropdown for adjusting prefix detection levels.
    """
    
    def __init__(self, csv_filename):
        self.csv_filename = csv_filename
        self.metric_data = []
        self.current_level = 1  # Current prefix level (1 = first part only, 2 = first two parts, etc.)
        self.max_level = 1
        
        # Load data
        self.load_data()
        
        # Create GUI
        self.setup_gui()
        
    def load_data(self):
        """Load metric data from CSV file."""
        with open(self.csv_filename, 'r') as csvfile:
            reader = csv.DictReader(csvfile)
            for row in reader:
                name = row['metric_name']
                value = int(row['value'])
                self.metric_data.append((name, value))
        
        # Calculate maximum meaningful prefix level
        max_parts = 0
        for name, _ in self.metric_data:
            if '_' in name:
                parts = len(name.split('_'))
                max_parts = max(max_parts, parts)
        
        self.max_level = max(1, max_parts - 1)  # Leave at least one part as suffix
    
    def get_prefix_at_level(self, name, level):
        """
        Extract prefix at specified level.
        Level 1 = first part only (broadest view)
        Level 2 = first two parts
        etc.
        Higher levels = narrower, more detailed view
        """
        if '_' not in name:
            return 'other'
        
        parts = name.split('_')
        
        if level >= len(parts):
            # If level is too high, use all but last part
            if len(parts) > 1:
                return '_'.join(parts[:-1]) + '_'
            else:
                return parts[0] + '_'
        
        # Take the first 'level' parts as prefix
        prefix_parts = parts[:level]
        return '_'.join(prefix_parts) + '_'
    
    def group_data_at_level(self, level):
        """Group metric data by prefix at specified level."""
        groups = defaultdict(int)
        
        for name, value in self.metric_data:
            prefix = self.get_prefix_at_level(name, level)
            groups[prefix] += value
            
        return groups
    
    def update_chart(self):
        """Update the pie chart with current level."""
        # Clear the current plot
        self.ax.clear()
        
        # Group data at current level
        prefix_groups = self.group_data_at_level(self.current_level)
        
        # Prepare data for pie chart
        labels = list(prefix_groups.keys())
        values = list(prefix_groups.values())
        
        # Create pie chart
        wedges, texts, autotexts = self.ax.pie(values, labels=labels, autopct='%1.1f%%', startangle=140)
        
        # Update title and info
        level_desc = f"Level {self.current_level}"
        if self.current_level == 1:
            level_desc += " (Broad view - first part only)"
        elif self.current_level == self.max_level:
            level_desc += " (Narrow view - most detailed)"
        else:
            level_desc += f" (First {self.current_level} parts)"
            
        self.ax.set_title(f'Metric Values Grouped by Prefix - {level_desc}')
        
        # Update info label
        num_groups = len(prefix_groups)
        self.info_label.config(text=f"Total metrics: {len(self.metric_data)} | Groups at this level: {num_groups}")
        
        # Refresh the plot
        self.fig.canvas.draw()
    
    def on_level_change(self, event=None):
        """Handle dropdown selection change."""
        try:
            new_level = int(self.level_var.get().split()[1])  # Extract number from "Level X"
            if new_level != self.current_level:
                self.current_level = new_level
                self.update_chart()
        except (ValueError, IndexError):
            pass
    
    def show_sample_metrics(self):
        """Show sample metrics at current level for debugging."""
        print(f"\nSample metrics at Level {self.current_level}:")
        sample_count = 0
        for name, value in self.metric_data[:10]:  # Show first 10
            prefix = self.get_prefix_at_level(name, self.current_level)
            print(f"  {name} -> {prefix}")
            sample_count += 1
        if len(self.metric_data) > 10:
            print(f"  ... and {len(self.metric_data) - 10} more")
    
    def setup_gui(self):
        """Set up the GUI with matplotlib and tkinter."""
        # Create main window
        self.root = tk.Tk()
        self.root.title("Interactive Metric Visualizer")
        self.root.geometry("1000x700")
        
        # Create control frame
        control_frame = ttk.Frame(self.root)
        control_frame.pack(side=tk.TOP, fill=tk.X, padx=10, pady=5)
        
        # Level selection dropdown
        ttk.Label(control_frame, text="Prefix Level:").pack(side=tk.LEFT, padx=(0, 5))
        
        self.level_var = tk.StringVar()
        level_options = []
        for i in range(1, self.max_level + 1):
            if i == 1:
                level_options.append(f"Level {i} (Broad)")
            elif i == self.max_level:
                level_options.append(f"Level {i} (Narrow)")
            else:
                level_options.append(f"Level {i}")
        
        self.level_dropdown = ttk.Combobox(control_frame, textvariable=self.level_var, 
                                          values=level_options, state="readonly", width=20)
        self.level_dropdown.pack(side=tk.LEFT, padx=(0, 10))
        self.level_dropdown.set(level_options[0])  # Set default to Level 1
        self.level_dropdown.bind('<<ComboboxSelected>>', self.on_level_change)
        
        # Debug button
        debug_btn = ttk.Button(control_frame, text="Show Sample", command=self.show_sample_metrics)
        debug_btn.pack(side=tk.LEFT, padx=(10, 0))
        
        # Info label
        self.info_label = ttk.Label(control_frame, text=f"Total metrics: {len(self.metric_data)}")
        self.info_label.pack(side=tk.RIGHT)
        
        # Create matplotlib figure
        self.fig, self.ax = plt.subplots(figsize=(10, 8))
        
        # Embed matplotlib in tkinter
        from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
        self.canvas = FigureCanvasTkAgg(self.fig, self.root)
        self.canvas.get_tk_widget().pack(side=tk.BOTTOM, fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # Initial chart
        self.update_chart()
    
    def run(self):
        """Start the GUI."""
        self.root.mainloop()


def main():
    """Main function to run the interactive visualizer."""
    if len(sys.argv) != 2:
        print("Usage: python interactive_visualizer.py <csv_file>")
        print("Example: python interactive_visualizer.py demo_custom.csv")
        sys.exit(1)
    
    csv_file = sys.argv[1]
    
    try:
        visualizer = InteractiveMetricVisualizer(csv_file)
        visualizer.run()
    except FileNotFoundError:
        print(f"Error: File '{csv_file}' not found.")
        sys.exit(1)
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
