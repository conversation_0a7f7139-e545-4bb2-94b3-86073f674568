import csv
import matplotlib.pyplot as plt
from collections import defaultdict
import sys
import numpy as np
from matplotlib.animation import FuncAnimation
# Function to detect all unique suffixes from all metric names
def detect_all_suffixes(metric_names):
    """
    Analyze all metric names to detect all unique suffix patterns.
    Returns a set of all unique suffixes found in the data.
    """
    # Collect all possible suffixes (last 1-3 parts of metric names)
    all_suffixes = set()

    for name in metric_names:
        if '_' not in name:
            continue

        parts = name.split('_')
        if len(parts) < 2:
            continue

        # Consider last 1-3 parts as potential suffixes
        for suffix_length in range(1, min(4, len(parts))):
            suffix = '_'.join(parts[-suffix_length:])
            all_suffixes.add(suffix)

    return all_suffixes


# Function to extract prefix using dynamically detected suffixes
def get_prefix_with_detected_suffixes(name, detected_suffixes):
    """
    Extract prefix using dynamically detected common suffixes.
    """
    if '_' not in name:
        return 'other'

    parts = name.split('_')

    # If only two parts, use the first part as prefix
    if len(parts) == 2:
        return parts[0] + '_'

    # Look for detected suffix patterns from the end
    for suffix_length in range(1, min(4, len(parts))):
        potential_suffix = '_'.join(parts[-suffix_length:])

        if potential_suffix in detected_suffixes:
            # Everything before this suffix is the prefix
            prefix_parts = parts[:-suffix_length]
            return '_'.join(prefix_parts) + '_'

    # If no detected suffix found, use all but the last part as prefix
    if len(parts) > 2:
        return '_'.join(parts[:-1]) + '_'

    # Fallback to first part
    return parts[0] + '_'


# Function to extract prefix (will be set dynamically)
get_prefix = None
# Function to read and group data from a CSV file
def read_grouped_data(csv_path):
    prefix_groups = defaultdict(int)
    with open(csv_path, 'r') as csvfile:
        reader = csv.DictReader(csvfile)
        for row in reader:
            name = row['metric_name']
            value = int(row['value'])
            prefix = get_prefix(name)
            prefix_groups[prefix] += value
    return prefix_groups
if __name__ == "__main__":
    if len(sys.argv) != 3:
        print("Usage: python display_csv_data.py <file1.csv> <file2.csv>")
        sys.exit(1)
    file1, file2 = sys.argv[1], sys.argv[2]

    # First pass: collect all metric names to detect common suffixes
    all_metric_names = []

    for csv_file in [file1, file2]:
        with open(csv_file, 'r') as csvfile:
            reader = csv.DictReader(csvfile)
            for row in reader:
                all_metric_names.append(row['metric_name'])

    # Detect all unique suffixes from all metric names
    detected_suffixes = detect_all_suffixes(all_metric_names)
    print(f"Detected unique suffixes: {sorted(detected_suffixes)}")

    # Set up the prefix extraction function with detected suffixes
    def get_prefix(name):
        return get_prefix_with_detected_suffixes(name, detected_suffixes)

    # Now read and group the data using dynamic prefix detection
    data1 = read_grouped_data(file1)
    data2 = read_grouped_data(file2)
    # Ensure both have the same set of labels
    all_labels = sorted(set(data1.keys()) | set(data2.keys()))
    values1 = np.array([data1.get(label, 0) for label in all_labels])
    values2 = np.array([data2.get(label, 0) for label in all_labels])
    fig, ax = plt.subplots(figsize=(8, 8))
    wedges, texts, autotexts = ax.pie(values1, labels=all_labels, autopct='%1.1f%%', startangle=140)
    plt.title('Metric Values Grouped by Detected Prefix (Animated)')
    plt.axis('equal')
    def animate(frame):
        # Interpolate between the two sets of values
        alpha = frame / 30  # 30 frames
        current_values = (1 - alpha) * values1 + alpha * values2
        # Remove old pie and draw new one
        ax.clear()
        wedges, texts, autotexts = ax.pie(current_values, labels=all_labels, autopct='%1.1f%%', startangle=140)
        plt.title('Metric Values Grouped by Detected Prefix (Animated)')
        plt.axis('equal')
        return wedges + texts + autotexts
    anim = FuncAnimation(fig, animate, frames=31, interval=100, blit=False, repeat=False)
    plt.show()