import csv
import random
import argparse
import os
from datetime import datetime, timedelta
from typing import List, Dict, Tuple

class MetricGenerator:
    """
    Generates CSV files with metrics that have various prefixes and some without prefixes.
    Designed to work with the csv_visualizer.py for grouped analysis.
    """

    def __init__(self):
        # Define metric prefixes and their typical ranges
        self.prefix_configs = {
            'cpu_': {'min': 10, 'max': 100, 'count_range': (3, 8)},
            'memory_': {'min': 500, 'max': 8000, 'count_range': (2, 6)},
            'disk_': {'min': 50, 'max': 1000, 'count_range': (2, 5)},
            'network_': {'min': 1, 'max': 500, 'count_range': (3, 7)},
            'api_': {'min': 100, 'max': 5000, 'count_range': (4, 10)},
            'db_': {'min': 50, 'max': 2000, 'count_range': (3, 8)},
            'cache_': {'min': 10, 'max': 1000, 'count_range': (2, 5)},
        }

        # Metrics without prefixes
        self.standalone_metrics = [
            'total_requests', 'active_users', 'error_count', 'uptime_seconds',
            'response_time', 'throughput', 'latency', 'availability',
            'concurrent_connections', 'queue_size'
        ]

        # Specific metric suffixes for each prefix
        self.metric_suffixes = {
            'cpu_': ['usage_percent', 'cores_active', 'load_average', 'idle_time', 'system_time', 'user_time'],
            'memory_': ['used_mb', 'available_mb', 'cached_mb', 'buffer_mb', 'swap_used'],
            'disk_': ['read_iops', 'write_iops', 'used_gb', 'free_gb'],
            'network_': ['bytes_in', 'bytes_out', 'packets_in', 'packets_out', 'errors', 'drops'],
            'api_': ['requests_per_sec', 'avg_response_time', 'error_rate', 'timeout_count', 'success_rate', 'queue_depth'],
            'db_': ['connections_active', 'queries_per_sec', 'slow_queries', 'deadlocks', 'cache_hit_ratio'],
            'cache_': ['hit_ratio', 'miss_count', 'evictions', 'memory_used']
        }

    def generate_metrics(self, include_prefixed: bool = True, include_standalone: bool = True,
                        custom_prefixes: List[str] = None) -> List[Tuple[str, int]]:
        """
        Generate a list of metric name-value pairs.

        Args:
            include_prefixed: Whether to include metrics with prefixes
            include_standalone: Whether to include standalone metrics
            custom_prefixes: Additional custom prefixes to include

        Returns:
            List of (metric_name, value) tuples
        """
        metrics = []

        # Generate prefixed metrics
        if include_prefixed:
            for prefix, config in self.prefix_configs.items():
                count = random.randint(*config['count_range'])
                suffixes = random.sample(self.metric_suffixes[prefix],
                                       min(count, len(self.metric_suffixes[prefix])))

                for suffix in suffixes:
                    metric_name = f"{prefix}{suffix}"
                    value = random.randint(config['min'], config['max'])
                    metrics.append((metric_name, value))

        # Generate custom prefixed metrics
        if custom_prefixes:
            for prefix in custom_prefixes:
                count = random.randint(2, 6)
                for i in range(count):
                    metric_name = f"{prefix}_metric_{i+1}"
                    value = random.randint(10, 1000)
                    metrics.append((metric_name, value))

        # Generate standalone metrics
        if include_standalone:
            standalone_count = random.randint(3, len(self.standalone_metrics))
            selected_standalone = random.sample(self.standalone_metrics, standalone_count)

            for metric in selected_standalone:
                value = random.randint(1, 10000)
                metrics.append((metric, value))

        return metrics

    def write_csv(self, metrics: List[Tuple[str, int]], filename: str,
                  include_timestamp: bool = False) -> None:
        """
        Write metrics to a CSV file.

        Args:
            metrics: List of (metric_name, value) tuples
            filename: Output CSV filename
            include_timestamp: Whether to include a timestamp column
        """
        os.makedirs(os.path.dirname(filename) if os.path.dirname(filename) else '.', exist_ok=True)

        with open(filename, 'w', newline='') as csvfile:
            fieldnames = ['metric_name', 'value']
            if include_timestamp:
                fieldnames.append('timestamp')

            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()

            for metric_name, value in metrics:
                row = {'metric_name': metric_name, 'value': value}
                if include_timestamp:
                    row['timestamp'] = datetime.now().isoformat()
                writer.writerow(row)

        print(f"Generated {len(metrics)} metrics in {filename}")

    def generate_time_series(self, base_metrics: List[Tuple[str, int]],
                           num_snapshots: int = 5,
                           variation_percent: float = 0.2) -> List[List[Tuple[str, int]]]:
        """
        Generate multiple snapshots of metrics with slight variations for time series analysis.

        Args:
            base_metrics: Base metrics to vary
            num_snapshots: Number of time snapshots to generate
            variation_percent: How much to vary values (0.2 = ±20%)

        Returns:
            List of metric snapshots
        """
        snapshots = []

        for i in range(num_snapshots):
            snapshot = []
            for metric_name, base_value in base_metrics:
                # Add some variation
                variation = int(base_value * variation_percent * (random.random() - 0.5) * 2)
                new_value = max(1, base_value + variation)
                snapshot.append((metric_name, new_value))
            snapshots.append(snapshot)

        return snapshots


def main():
    """
    Main function with command-line interface for generating metric CSV files.
    """
    parser = argparse.ArgumentParser(description='Generate CSV files with metrics for analysis')
    parser.add_argument('--output', '-o', default='metrics.csv',
                       help='Output CSV filename (default: metrics.csv)')
    parser.add_argument('--count', '-c', type=int, default=1,
                       help='Number of CSV files to generate (default: 1)')
    parser.add_argument('--no-prefixed', action='store_true',
                       help='Exclude metrics with prefixes')
    parser.add_argument('--no-standalone', action='store_true',
                       help='Exclude standalone metrics')
    parser.add_argument('--custom-prefixes', nargs='+',
                       help='Additional custom prefixes to include')
    parser.add_argument('--timestamp', action='store_true',
                       help='Include timestamp column in CSV')
    parser.add_argument('--time-series', type=int, metavar='N',
                       help='Generate N time-series snapshots instead of single file')

    args = parser.parse_args()

    generator = MetricGenerator()

    if args.time_series:
        # Generate time series data
        base_metrics = generator.generate_metrics(
            include_prefixed=not args.no_prefixed,
            include_standalone=not args.no_standalone,
            custom_prefixes=args.custom_prefixes
        )

        snapshots = generator.generate_time_series(base_metrics, args.time_series)

        for i, snapshot in enumerate(snapshots):
            filename = f"{args.output.rsplit('.', 1)[0]}_t{i+1}.csv"
            generator.write_csv(snapshot, filename, args.timestamp)

    else:
        # Generate single or multiple independent files
        for i in range(args.count):
            metrics = generator.generate_metrics(
                include_prefixed=not args.no_prefixed,
                include_standalone=not args.no_standalone,
                custom_prefixes=args.custom_prefixes
            )

            if args.count == 1:
                filename = args.output
            else:
                base_name = args.output.rsplit('.', 1)[0]
                extension = args.output.rsplit('.', 1)[1] if '.' in args.output else 'csv'
                filename = f"{base_name}_{i+1}.{extension}"

            generator.write_csv(metrics, filename, args.timestamp)


def demo_usage():
    """
    Demonstrate various usage patterns of the MetricGenerator.
    """
    print("=== MetricGenerator Demo ===\n")

    generator = MetricGenerator()

    # Example 1: Basic metrics with prefixes and standalone
    print("1. Generating basic metrics...")
    metrics1 = generator.generate_metrics()
    generator.write_csv(metrics1, 'demo_basic.csv')

    # Example 2: Only prefixed metrics
    print("\n2. Generating only prefixed metrics...")
    metrics2 = generator.generate_metrics(include_standalone=False)
    generator.write_csv(metrics2, 'demo_prefixed_only.csv')

    # Example 3: Custom prefixes
    print("\n3. Generating metrics with custom prefixes...")
    metrics3 = generator.generate_metrics(custom_prefixes=['custom', 'app', 'service'])
    generator.write_csv(metrics3, 'demo_custom.csv')

    # Example 4: Time series data
    print("\n4. Generating time series data...")
    base_metrics = generator.generate_metrics()
    time_series = generator.generate_time_series(base_metrics, num_snapshots=3)

    for i, snapshot in enumerate(time_series):
        generator.write_csv(snapshot, f'demo_timeseries_{i+1}.csv', include_timestamp=True)

    print("\n=== Demo Complete ===")
    print("Generated files:")
    print("- demo_basic.csv")
    print("- demo_prefixed_only.csv")
    print("- demo_custom.csv")
    print("- demo_timeseries_1.csv, demo_timeseries_2.csv, demo_timeseries_3.csv")
    print("\nYou can now use these with csv_visualizer.py:")
    print("python csv_visualizer.py demo_timeseries_1.csv demo_timeseries_2.csv")


if __name__ == "__main__":
    import sys

    # If no arguments provided, run demo
    if len(sys.argv) == 1:
        demo_usage()
    else:
        main()