#!/usr/bin/env python3
"""
Test script to demonstrate the improved prefix detection logic.
"""

def get_prefix(name):
    """
    Extract prefix by identifying common suffixes and treating everything before them as prefix.
    This continues until we reach a recognizable suffix pattern.
    """
    if '_' not in name:
        return 'other'
    
    # Common metric suffixes that indicate the end of the prefix
    common_suffixes = {
        # Time-related
        'time', 'seconds', 'ms', 'minutes', 'hours',
        # Percentages and ratios
        'percent', 'percentage', 'ratio', 'rate',
        # Counts and quantities
        'count', 'total', 'number', 'num',
        # Size and capacity
        'size', 'bytes', 'mb', 'gb', 'tb', 'kb',
        # Performance metrics
        'iops', 'ops', 'per_sec', 'throughput', 'latency',
        # Status indicators
        'active', 'idle', 'used', 'free', 'available',
        # Network metrics
        'in', 'out', 'errors', 'drops', 'packets',
        # Database metrics
        'queries', 'connections', 'deadlocks', 'hits', 'misses',
        # Generic numbered metrics
        '1', '2', '3', '4', '5', '6', '7', '8', '9'
    }
    
    parts = name.split('_')
    
    # If only two parts, use the first part as prefix
    if len(parts) == 2:
        return parts[0] + '_'
    
    # Look for suffix patterns from the end
    for i in range(len(parts) - 1, 0, -1):
        potential_suffix = parts[i]
        
        # Check if this part looks like a suffix
        if potential_suffix.lower() in common_suffixes:
            # Everything before this suffix is the prefix
            prefix_parts = parts[:i]
            return '_'.join(prefix_parts) + '_'
        
        # Check for compound suffixes (like "hit_ratio", "per_sec")
        if i > 1:
            compound_suffix = '_'.join(parts[i-1:i+1])
            if compound_suffix.lower() in common_suffixes:
                prefix_parts = parts[:i-1]
                return '_'.join(prefix_parts) + '_'
    
    # If no recognizable suffix found, use all but the last part as prefix
    if len(parts) > 2:
        return '_'.join(parts[:-1]) + '_'
    
    # Fallback to first part
    return parts[0] + '_'


def test_prefix_detection():
    """Test the prefix detection with various metric names."""
    
    test_cases = [
        # Basic cases
        'cpu_usage_percent',
        'memory_used_mb', 
        'disk_read_iops',
        'network_bytes_in',
        
        # More complex cases
        'api_avg_response_time',
        'db_cache_hit_ratio',
        'cache_memory_used',
        'cpu_system_time',
        
        # Custom metrics
        'custom_metric_1',
        'app_metric_2',
        'service_metric_3',
        
        # Standalone metrics
        'total_requests',
        'active_users',
        'error_count',
        
        # Edge cases
        'single_word',
        'two_parts',
        'very_long_metric_name_with_many_parts_count'
    ]
    
    print("=== Prefix Detection Test Results ===\n")
    print(f"{'Metric Name':<40} {'Detected Prefix':<20}")
    print("-" * 60)
    
    for metric in test_cases:
        prefix = get_prefix(metric)
        print(f"{metric:<40} {prefix:<20}")
    
    print("\n=== Grouping Example ===")
    from collections import defaultdict
    
    groups = defaultdict(list)
    for metric in test_cases:
        prefix = get_prefix(metric)
        groups[prefix].append(metric)
    
    for prefix, metrics in sorted(groups.items()):
        print(f"\n{prefix}:")
        for metric in metrics:
            print(f"  - {metric}")


if __name__ == "__main__":
    test_prefix_detection()
