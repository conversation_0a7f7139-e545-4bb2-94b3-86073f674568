<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Interactive Metric Visualizer</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .controls {
            display: flex;
            align-items: center;
            gap: 20px;
            margin-bottom: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
        .control-group {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        select {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        button {
            padding: 8px 16px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .info {
            margin-left: auto;
            font-size: 14px;
            color: #666;
        }
        .chart-container {
            position: relative;
            height: 500px;
            margin-top: 20px;
        }
        .file-input {
            margin-bottom: 20px;
            padding: 15px;
            border: 2px dashed #ddd;
            border-radius: 5px;
            text-align: center;
        }
        .sample-data {
            margin-top: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Interactive Metric Visualizer</h1>
        <p>Upload a CSV file with metric_name and value columns, then use the dropdown to adjust prefix detection levels for broad or narrow views.</p>
        
        <div class="file-input">
            <input type="file" id="csvFile" accept=".csv" />
            <p>Or <button onclick="loadSampleData()">Load Sample Data</button></p>
        </div>
        
        <div class="controls" id="controls" style="display: none;">
            <div class="control-group">
                <label for="levelSelect">Prefix Level:</label>
                <select id="levelSelect" onchange="updateChart()">
                    <!-- Options will be populated dynamically -->
                </select>
            </div>
            <button onclick="showSampleMetrics()">Show Sample Mappings</button>
            <div class="info" id="info">
                <!-- Info will be populated dynamically -->
            </div>
        </div>
        
        <div class="chart-container" id="chartContainer" style="display: none;">
            <canvas id="metricChart"></canvas>
        </div>
        
        <div class="sample-data" id="sampleData" style="display: none;">
            <!-- Sample data will be shown here -->
        </div>
    </div>

    <script>
        let metricData = [];
        let chart = null;
        let currentLevel = 1;
        let maxLevel = 1;

        // Sample data for demonstration
        const sampleMetrics = [
            ['cpu_user_time', 95], ['cpu_system_time', 73], ['cpu_cores_active', 51],
            ['cpu_load_average', 69], ['cpu_usage_percent', 11], ['cpu_idle_time', 17],
            ['memory_buffer_mb', 7430], ['memory_used_mb', 3672], ['memory_cached_mb', 5474],
            ['memory_swap_used', 6487], ['disk_read_iops', 965], ['disk_free_gb', 428],
            ['disk_write_iops', 841], ['disk_used_gb', 443], ['network_bytes_in', 476],
            ['network_errors', 134], ['network_drops', 93], ['api_avg_response_time', 2901],
            ['api_success_rate', 2082], ['api_timeout_count', 4663], ['api_requests_per_sec', 1072],
            ['api_queue_depth', 157], ['api_error_rate', 4404], ['db_deadlocks', 1872],
            ['db_slow_queries', 323], ['db_cache_hit_ratio', 626], ['db_queries_per_sec', 1535],
            ['db_connections_active', 1632], ['cache_hit_ratio', 912], ['cache_memory_used', 704],
            ['total_requests', 7557], ['concurrent_connections', 7019], ['error_count', 4973],
            ['active_users', 3461], ['throughput', 5265]
        ];

        function loadSampleData() {
            metricData = sampleMetrics;
            processData();
        }

        function getPrefixAtLevel(name, level) {
            if (!name.includes('_')) {
                return 'other';
            }
            
            const parts = name.split('_');
            
            if (level >= parts.length) {
                if (parts.length > 1) {
                    return parts.slice(0, -1).join('_') + '_';
                } else {
                    return parts[0] + '_';
                }
            }
            
            return parts.slice(0, level).join('_') + '_';
        }

        function groupDataAtLevel(level) {
            const groups = {};
            
            metricData.forEach(([name, value]) => {
                const prefix = getPrefixAtLevel(name, level);
                groups[prefix] = (groups[prefix] || 0) + value;
            });
            
            return groups;
        }

        function updateChart() {
            const levelSelect = document.getElementById('levelSelect');
            currentLevel = parseInt(levelSelect.value);
            
            const groups = groupDataAtLevel(currentLevel);
            const labels = Object.keys(groups);
            const values = Object.values(groups);
            
            // Generate colors
            const colors = labels.map((_, i) => 
                `hsl(${(i * 360 / labels.length)}, 70%, 60%)`
            );
            
            if (chart) {
                chart.destroy();
            }
            
            const ctx = document.getElementById('metricChart').getContext('2d');
            chart = new Chart(ctx, {
                type: 'pie',
                data: {
                    labels: labels,
                    datasets: [{
                        data: values,
                        backgroundColor: colors,
                        borderWidth: 2,
                        borderColor: '#fff'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: `Metric Values Grouped by Prefix - Level ${currentLevel} ${getLevelDescription(currentLevel)}`,
                            font: { size: 16 }
                        },
                        legend: {
                            position: 'right'
                        }
                    }
                }
            });
            
            // Update info
            document.getElementById('info').textContent = 
                `Total metrics: ${metricData.length} | Groups at this level: ${labels.length}`;
        }

        function getLevelDescription(level) {
            if (level === 1) {
                return '(Broad view - first part only)';
            } else if (level === maxLevel) {
                return '(Narrow view - most detailed)';
            } else {
                return `(First ${level} parts)`;
            }
        }

        function showSampleMetrics() {
            const sampleDiv = document.getElementById('sampleData');
            let html = `<h4>Sample metrics at Level ${currentLevel}:</h4>`;
            
            const sampleCount = Math.min(15, metricData.length);
            for (let i = 0; i < sampleCount; i++) {
                const [name, value] = metricData[i];
                const prefix = getPrefixAtLevel(name, currentLevel);
                html += `${name} → ${prefix}<br>`;
            }
            
            if (metricData.length > sampleCount) {
                html += `... and ${metricData.length - sampleCount} more`;
            }
            
            sampleDiv.innerHTML = html;
            sampleDiv.style.display = sampleDiv.style.display === 'none' ? 'block' : 'none';
        }

        function processData() {
            // Calculate max level
            maxLevel = 1;
            metricData.forEach(([name]) => {
                if (name.includes('_')) {
                    const parts = name.split('_').length;
                    maxLevel = Math.max(maxLevel, parts - 1);
                }
            });
            
            // Populate level dropdown
            const levelSelect = document.getElementById('levelSelect');
            levelSelect.innerHTML = '';
            
            for (let i = 1; i <= maxLevel; i++) {
                const option = document.createElement('option');
                option.value = i;
                option.textContent = `Level ${i} ${getLevelDescription(i)}`;
                levelSelect.appendChild(option);
            }
            
            // Show controls and chart
            document.getElementById('controls').style.display = 'flex';
            document.getElementById('chartContainer').style.display = 'block';
            
            // Initial chart
            currentLevel = 1;
            updateChart();
        }

        // File upload handling
        document.getElementById('csvFile').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const csv = e.target.result;
                    const lines = csv.split('\n');
                    const headers = lines[0].split(',');
                    
                    metricData = [];
                    for (let i = 1; i < lines.length; i++) {
                        if (lines[i].trim()) {
                            const values = lines[i].split(',');
                            const name = values[0].trim();
                            const value = parseInt(values[1]);
                            if (name && !isNaN(value)) {
                                metricData.push([name, value]);
                            }
                        }
                    }
                    
                    processData();
                };
                reader.readAsText(file);
            }
        });
    </script>
</body>
</html>
