metric_name,value
cpu_user_time,95
cpu_system_time,73
cpu_cores_active,51
cpu_load_average,69
cpu_usage_percent,11
cpu_idle_time,17
memory_buffer_mb,7430
memory_used_mb,3672
memory_cached_mb,5474
memory_swap_used,6487
disk_read_iops,965
disk_free_gb,428
disk_write_iops,841
disk_used_gb,443
network_bytes_in,476
network_errors,134
network_drops,93
api_avg_response_time,2901
api_success_rate,2082
api_timeout_count,4663
api_requests_per_sec,1072
api_queue_depth,157
api_error_rate,4404
db_deadlocks,1872
db_slow_queries,323
db_cache_hit_ratio,626
db_queries_per_sec,1535
db_connections_active,1632
cache_hit_ratio,912
cache_memory_used,704
custom_metric_1,214
custom_metric_2,784
custom_metric_3,399
app_metric_1,851
app_metric_2,39
app_metric_3,210
service_metric_1,20
service_metric_2,786
service_metric_3,351
total_requests,7557
concurrent_connections,7019
error_count,4973
active_users,3461
throughput,5265
