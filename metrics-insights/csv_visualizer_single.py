import csv
import matplotlib.pyplot as plt
from collections import defaultdict
from matplotlib.widgets import Button
import matplotlib.patches as patches

# Function to detect all unique suffixes from all metric names
def detect_all_suffixes(metric_names):
    """
    Analyze all metric names to detect all unique suffix patterns.
    Returns a set of all unique suffixes found in the data.
    """
    # Collect all possible suffixes (last 1-3 parts of metric names)
    all_suffixes = set()

    for name in metric_names:
        if '_' not in name:
            continue

        parts = name.split('_')
        if len(parts) < 2:
            continue

        # Consider last 1-3 parts as potential suffixes
        for suffix_length in range(1, min(4, len(parts))):
            suffix = '_'.join(parts[-suffix_length:])
            all_suffixes.add(suffix)

    return all_suffixes


# Function to extract prefix using dynamically detected suffixes
def get_prefix_with_detected_suffixes(name, detected_suffixes):
    """
    Extract prefix using dynamically detected common suffixes.
    """
    if '_' not in name:
        return 'other'

    parts = name.split('_')

    # If only two parts, use the first part as prefix
    if len(parts) == 2:
        return parts[0] + '_'

    # Look for detected suffix patterns from the end
    for suffix_length in range(1, min(4, len(parts))):
        potential_suffix = '_'.join(parts[-suffix_length:])

        if potential_suffix in detected_suffixes:
            # Everything before this suffix is the prefix
            prefix_parts = parts[:-suffix_length]
            return '_'.join(prefix_parts) + '_'

    # If no detected suffix found, use all but the last part as prefix
    if len(parts) > 2:
        return '_'.join(parts[:-1]) + '_'

    # Fallback to first part
    return parts[0] + '_'


# Read and group data from CSV by dynamic prefix detection
csv_filename = 'demo_custom.csv'

# First pass: collect all metric names to detect common suffixes
all_metric_names = []
with open(csv_filename, 'r') as csvfile:
    reader = csv.DictReader(csvfile)
    for row in reader:
        all_metric_names.append(row['metric_name'])

# Detect all unique suffixes from all metric names
detected_suffixes = detect_all_suffixes(all_metric_names)
print(f"Detected unique suffixes: {sorted(detected_suffixes)}")

# Second pass: group data using dynamic prefix detection

prefix_groups = defaultdict(int)
with open(csv_filename, 'r') as csvfile:
    reader = csv.DictReader(csvfile)
    for row in reader:
        name = row['metric_name']
        value = int(row['value'])
        prefix = get_prefix_with_detected_suffixes(name, detected_suffixes)
        prefix_groups[prefix] += value

# Prepare data for pie chart
labels = list(prefix_groups.keys())
values = list(prefix_groups.values())

# Plot pie chart
plt.figure(figsize=(8, 8))
plt.pie(values, labels=labels, autopct='%1.1f%%', startangle=140)
plt.title('Metric Values Grouped by Detected Prefix')
plt.axis('equal')
plt.show()