#!/usr/bin/env python3
"""
Test script to verify that the app ONLY uses specific projects when configured
"""

import sys
import os
sys.path.append(os.path.dirname(__file__))

from main import GitLabAPI

def test_specific_projects_only():
    """Test that specific project configuration bypasses general project discovery"""
    
    print("🎯 Testing Specific Projects Only Mode")
    print("=" * 50)
    
    # Get credentials
    url = input("Enter GitLab URL: ").strip()
    token = input("Enter Access Token: ").strip()
    
    if not url or not token:
        print("❌ URL and token are required")
        return
    
    print(f"\n🔗 Testing connection to: {url}")
    
    # Create API instance
    api = GitLabAPI(url, token, verify_ssl=False)
    
    # Test connection
    success, message = api.test_connection()
    if not success:
        print(f"❌ Connection failed: {message}")
        return
    print(f"✅ Connection successful: {message}")
    
    # Test 1: General project discovery (what we want to avoid)
    print("\n📋 Test 1: General project discovery (old behavior)")
    print("This is what happens when NO specific projects are configured:")
    general_projects = api.get_user_projects(None)
    print(f"Result: Found {len(general_projects)} projects via general discovery")
    
    # Test 2: Specific project mode (what we want)
    print("\n🎯 Test 2: Specific projects only (new behavior)")
    specific_projects_input = input("Enter specific project IDs or names (comma-separated): ").strip()
    
    if not specific_projects_input:
        print("❌ No specific projects provided - cannot test specific mode")
        return
    
    print(f"Testing with specific projects: {specific_projects_input}")
    print("This should ONLY fetch the specified projects, no general discovery!")
    
    specific_projects = api.get_user_projects(specific_projects_input)
    print(f"Result: Found {len(specific_projects)} specific projects")
    
    # Show the results
    print("\n📊 Comparison:")
    print(f"  General discovery: {len(general_projects)} projects")
    print(f"  Specific projects: {len(specific_projects)} projects")
    
    if len(specific_projects) < len(general_projects):
        print("✅ SUCCESS: Specific mode returned fewer projects (as expected)")
        print("✅ The app is now only checking your specified projects!")
    elif len(specific_projects) == len(general_projects):
        print("⚠️  WARNING: Same number of projects found")
        print("   This might mean your specific projects include all accessible projects")
    else:
        print("❌ ERROR: Specific mode returned more projects than general discovery")
        print("   This shouldn't happen - please check the implementation")
    
    # Show specific projects found
    if specific_projects:
        print(f"\n📁 Specific projects found:")
        for i, project in enumerate(specific_projects):
            print(f"  {i+1}. {project.get('name', 'Unknown')} (ID: {project.get('id', 'Unknown')})")
    
    print("\n" + "=" * 50)
    print("🎯 Test completed!")
    print("The app should now ONLY check the projects you specified,")
    print("without scanning for all projects you're a member of.")

if __name__ == "__main__":
    try:
        test_specific_projects_only()
    except KeyboardInterrupt:
        print("\n❌ Test interrupted by user")
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
