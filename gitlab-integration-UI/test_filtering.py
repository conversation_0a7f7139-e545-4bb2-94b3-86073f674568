#!/usr/bin/env python3
"""
Test script to verify project and author filtering functionality
"""

import sys
import os
sys.path.append(os.path.dirname(__file__))

from main import GitLabAPI

def test_filtering():
    """Test the new filtering features"""
    
    print("🔍 GitLab Filtering Test")
    print("=" * 50)
    
    # Get credentials
    url = input("Enter GitLab URL: ").strip()
    token = input("Enter Access Token: ").strip()
    
    if not url or not token:
        print("❌ URL and token are required")
        return
    
    print(f"\n🔗 Testing connection to: {url}")
    
    # Create API instance
    api = GitLabAPI(url, token, verify_ssl=False)
    
    # Test connection
    success, message = api.test_connection()
    if not success:
        print(f"❌ Connection failed: {message}")
        return
    print(f"✅ Connection successful: {message}")
    
    # Get user info for author filtering
    print("\n👤 Getting user information...")
    try:
        import requests
        response = requests.get(
            f"{url}/api/v4/user",
            headers={'Authorization': f'Bearer {token}'},
            verify=False
        )
        if response.status_code == 200:
            user_data = response.json()
            user_id = user_data.get('id')
            username = user_data.get('username')
            print(f"✅ User: {username} (ID: {user_id})")
        else:
            print("❌ Could not get user info")
            return
    except Exception as e:
        print(f"❌ Error getting user info: {e}")
        return
    
    # Test specific project filtering
    print("\n📁 Testing project filtering...")
    specific_projects = input("Enter specific project IDs or names (comma-separated, or press Enter for all): ").strip()
    
    if specific_projects:
        print(f"🔍 Searching for specific projects: {specific_projects}")
        projects = api.get_user_projects(specific_projects)
    else:
        print("🔍 Getting all accessible projects...")
        projects = api.get_user_projects()
    
    print(f"✅ Found {len(projects)} projects")
    
    if not projects:
        print("❌ No projects found - cannot test MR filtering")
        return
    
    # Show projects
    print("\n📋 Projects found:")
    for i, project in enumerate(projects[:5]):  # Show first 5
        print(f"  {i+1}. {project.get('name', 'Unknown')} (ID: {project.get('id', 'Unknown')})")
    
    if len(projects) > 5:
        print(f"  ... and {len(projects) - 5} more projects")
    
    # Test MR filtering
    print(f"\n🔄 Testing MR filtering for user {username}...")
    
    total_all_mrs = 0
    total_my_mrs = 0
    
    for project in projects[:3]:  # Test first 3 projects
        project_name = project.get('name', 'Unknown')
        project_id = project.get('id')
        
        print(f"\n  📂 Project: {project_name}")
        
        # Get all MRs
        all_mrs = api.get_merge_requests(project_id, 'opened')
        total_all_mrs += len(all_mrs)
        print(f"    All opened MRs: {len(all_mrs)}")
        
        # Get only user's MRs
        my_mrs = api.get_merge_requests(project_id, 'opened', user_id)
        total_my_mrs += len(my_mrs)
        print(f"    My opened MRs: {len(my_mrs)}")
        
        # Show sample MRs
        if my_mrs:
            print("    Sample of my MRs:")
            for mr in my_mrs[:2]:  # Show first 2
                print(f"      - {mr.title} (by {mr.author})")
    
    print(f"\n📊 Summary:")
    print(f"  Total opened MRs in tested projects: {total_all_mrs}")
    print(f"  My opened MRs in tested projects: {total_my_mrs}")
    print(f"  Filtering efficiency: {(total_my_mrs/total_all_mrs*100):.1f}%" if total_all_mrs > 0 else "  No MRs to filter")
    
    print("\n✅ Filtering test completed!")

if __name__ == "__main__":
    try:
        test_filtering()
    except KeyboardInterrupt:
        print("\n❌ Test interrupted by user")
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
