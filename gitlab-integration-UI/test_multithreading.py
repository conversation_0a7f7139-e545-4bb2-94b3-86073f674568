#!/usr/bin/env python3
"""
Test script to demonstrate multithreading performance improvements
"""

import time
import json
import os
from main import GitLabAPI, Config<PERSON><PERSON><PERSON>

def test_multithreading_performance():
    """Test and compare sequential vs concurrent performance"""
    
    # Load configuration
    config_manager = ConfigManager()
    config = config_manager.load_config()
    
    if not config.get('gitlab_url') or not config.get('gitlab_token'):
        print("❌ No GitLab configuration found. Please run the main app first to configure.")
        return
    
    # Initialize API
    api = GitLabAPI(
        config['gitlab_url'], 
        config['gitlab_token'], 
        config.get('verify_ssl', False)
    )
    
    # Test connection
    success, message = api.test_connection()
    if not success:
        print(f"❌ Connection failed: {message}")
        return
    
    print("✅ Connected to GitLab successfully")
    
    # Get specific projects from config
    specific_projects = config.get('specific_projects', '')
    if not specific_projects:
        print("❌ No specific projects configured. Please configure some projects first.")
        return
    
    print(f"🔍 Testing with projects: {specific_projects}")
    
    # Test project discovery performance
    print("\n" + "="*60)
    print("📊 PROJECT DISCOVERY PERFORMANCE TEST")
    print("="*60)
    
    start_time = time.time()
    projects = api._get_specific_projects(specific_projects)
    concurrent_time = time.time() - start_time
    
    print(f"✅ Concurrent project discovery: {concurrent_time:.2f}s")
    print(f"📁 Found {len(projects)} projects")
    
    if not projects:
        print("❌ No projects found")
        return
    
    # Test MR fetching performance
    print("\n" + "="*60)
    print("📊 MERGE REQUEST FETCHING PERFORMANCE TEST")
    print("="*60)
    
    # Get user info for author filtering
    user_response = api.test_connection()
    if user_response[0]:
        # Get user ID for filtering
        import requests
        user_resp = requests.get(
            f"{api.base_url}/api/v4/user",
            headers=api.headers,
            verify=api.verify_ssl
        )
        user_id = user_resp.json().get('id') if user_resp.status_code == 200 else None
    else:
        user_id = None
    
    # Test concurrent MR fetching
    print(f"🔍 Testing MR fetching for {len(projects)} projects...")
    
    start_time = time.time()
    total_mrs = 0
    
    # Simulate the concurrent fetching (similar to refresh_merge_requests)
    from concurrent.futures import ThreadPoolExecutor, as_completed
    
    fetch_tasks = []
    for project in projects:
        fetch_tasks.append((project, 'opened', user_id))
    
    with ThreadPoolExecutor(max_workers=min(8, len(fetch_tasks))) as executor:
        future_to_task = {
            executor.submit(api.get_merge_requests, project['id'], state, author_id): (project, state)
            for project, state, author_id in fetch_tasks
        }
        
        for future in as_completed(future_to_task):
            project, state = future_to_task[future]
            try:
                mrs = future.result()
                total_mrs += len(mrs)
                print(f"  ✓ {project.get('name', 'Unknown')} ({state}): {len(mrs)} MRs")
            except Exception as e:
                print(f"  ✗ {project.get('name', 'Unknown')} ({state}): Error - {e}")
    
    concurrent_mr_time = time.time() - start_time
    
    print(f"\n✅ Concurrent MR fetching: {concurrent_mr_time:.2f}s")
    print(f"📋 Total MRs found: {total_mrs}")
    
    # Summary
    print("\n" + "="*60)
    print("📈 PERFORMANCE SUMMARY")
    print("="*60)
    print(f"🚀 Project Discovery: {concurrent_time:.2f}s ({len(projects)} projects)")
    print(f"🚀 MR Fetching: {concurrent_mr_time:.2f}s ({total_mrs} MRs)")
    print(f"🚀 Total Time: {(concurrent_time + concurrent_mr_time):.2f}s")
    print(f"⚡ Average per project: {((concurrent_time + concurrent_mr_time) / len(projects)):.2f}s")
    
    # Estimate sequential performance
    estimated_sequential = len(projects) * 2.0  # Rough estimate
    improvement = estimated_sequential / (concurrent_time + concurrent_mr_time)
    print(f"📊 Estimated speedup: {improvement:.1f}x faster than sequential")

if __name__ == "__main__":
    test_multithreading_performance()
