#!/usr/bin/env python3
"""
Run the GitLab MR Monitor with debug output visible
"""

import sys
import os

# Redirect stdout to both console and file for debugging
class DebugLogger:
    def __init__(self):
        self.terminal = sys.stdout
        self.log = open("debug.log", "w")

    def write(self, message):
        self.terminal.write(message)
        self.log.write(message)
        self.log.flush()

    def flush(self):
        self.terminal.flush()
        self.log.flush()

# Set up debug logging
sys.stdout = DebugLogger()

print("🐛 Starting GitLab MR Monitor with debug logging...")
print("📝 Debug output will be saved to debug.log")
print("=" * 50)

try:
    from main import GitLabMRMonitor
    app = GitLabMRMonitor()
    app.run()
except Exception as e:
    print(f"❌ Application error: {e}")
    import traceback
    traceback.print_exc()
finally:
    print("=" * 50)
    print("🐛 Debug session ended")
    if hasattr(sys.stdout, 'log'):
        sys.stdout.log.close()
