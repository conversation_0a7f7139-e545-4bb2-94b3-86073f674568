#!/usr/bin/env python3
"""
Debug script to test GitLab API functionality
"""

import sys
import os
sys.path.append(os.path.dirname(__file__))

from main import GitLabAPI

def test_gitlab_connection():
    """Test GitLab connection and data fetching"""
    
    # Get credentials from user
    url = input("Enter GitLab URL (e.g., https://gitlab.com): ").strip()
    token = input("Enter Access Token: ").strip()
    
    if not url or not token:
        print("❌ URL and token are required")
        return
    
    print(f"\n🔗 Testing connection to: {url}")
    
    # Test connection
    api = GitLabAPI(url, token, verify_ssl=False)
    success, message = api.test_connection()
    
    if not success:
        print(f"❌ Connection failed: {message}")
        return
    
    print(f"✅ Connection successful: {message}")
    
    # Test project loading
    print("\n📁 Loading projects...")
    projects = api.get_user_projects()
    print(f"✅ Found {len(projects)} projects")
    
    if not projects:
        print("❌ No projects found - you may not have access to any projects")
        return
    
    # Show first few projects
    print("\n📋 First 5 projects:")
    for i, project in enumerate(projects[:5]):
        print(f"  {i+1}. {project.get('name', 'Unknown')} (ID: {project.get('id', 'Unknown')})")
    
    # Test MR loading for first project
    if projects:
        first_project = projects[0]
        project_name = first_project.get('name', 'Unknown')
        project_id = first_project.get('id')
        
        print(f"\n🔄 Testing MR loading for project: {project_name}")
        
        for state in ['opened', 'merged', 'closed']:
            mrs = api.get_merge_requests(project_id, state)
            print(f"  {state}: {len(mrs)} MRs")
            
            if mrs and state == 'opened':
                print("    Sample MR:")
                mr = mrs[0]
                print(f"      Title: {mr.title}")
                print(f"      Author: {mr.author}")
                print(f"      State: {mr.state}")
    
    print("\n✅ Debug test completed!")

if __name__ == "__main__":
    try:
        test_gitlab_connection()
    except KeyboardInterrupt:
        print("\n❌ Test interrupted by user")
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
