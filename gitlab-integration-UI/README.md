# GitLab Merge Request Monitor

A neat, comfortable UI to monitor all your open merge requests across GitLab projects with color-coded status labels.

## Features

- **Color-coded status labels**: Visual indicators for MR states (new/opened, merged, closed, draft)
- **Pipeline status indicators**: See CI/CD pipeline status at a glance
- **Advanced filtering**: Show only YOUR merge requests and monitor specific projects
- **Project targeting**: Specify exact projects to monitor (by ID or name) instead of scanning all
- **Author filtering**: Option to show only merge requests where you are the author
- **Real-time updates**: Auto-refresh functionality with configurable intervals
- **Quick actions**: Double-click to open MRs in browser, right-click for context menu
- **Persistent configuration**: Saves your GitLab credentials and filtering preferences

## Status Color Coding

- 🟢 **Green (Opened)**: New or open merge requests
- 🟣 **Purple (Merged)**: Successfully merged MRs
- 🔴 **Red (Closed)**: Closed without merging
- 🟡 **Yellow (Draft)**: Draft merge requests
- ⚫ **Gray (Locked)**: Locked merge requests

## Pipeline Status Colors

- 🟢 **Green**: Success
- 🔴 **Red**: Failed
- 🔵 **Blue**: Running
- 🟡 **Yellow**: Pending
- ⚫ **Gray**: Canceled/Skipped

## Filtering Configuration

### Author Filtering
- **"Only show my merge requests"**: When enabled, only shows MRs where you are the author
- Uses GitLab API's `author_id` parameter for efficient filtering
- Automatically detects your user ID from the access token

### Project Filtering
- **Specific Projects**: Enter comma-separated project IDs or names to monitor only those projects
- **Examples**:
  - By ID: `123,456,789`
  - By name: `my-project,other-project`
  - By full path: `group/my-project,group/other-project`
- **Leave empty** to monitor all accessible projects
- **Benefits**: Faster loading, reduced API calls, focused monitoring

### Combined Filtering
- Both filters work together: specific projects + only your MRs
- Perfect for monitoring your work across selected repositories
- Significantly reduces noise and improves performance

## Setup

### Prerequisites

- Python 3.7 or higher
- GitLab account with API access

### Installation

1. Clone or download this repository
2. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

### GitLab Access Token

1. Go to your GitLab instance → User Settings → Access Tokens
2. Create a new token with the following scopes:
   - `read_api`
   - `read_repository`
3. Copy the generated token (you'll need it for the application)

## Usage

1. Run the application:
   ```bash
   python main.py
   ```

2. On first run, configure your GitLab connection:
   - **GitLab URL**: Your GitLab instance URL (e.g., `https://gitlab.com` or `https://gitlab.yourcompany.com`)
   - **Access Token**: The token you created above
   - **SSL Verification**: Uncheck for self-hosted GitLab with self-signed certificates
   - Click "Test" to verify connection, then "Connect" to save

3. Once connected:
   - The application will automatically load your projects and merge requests
   - Use the filters to narrow down the view
   - Enable auto-refresh for real-time monitoring
   - Double-click any MR to open it in your browser

## Configuration

The application saves your configuration in `config.json` in the same directory. This includes:
- GitLab URL
- Access token (stored securely)
- Auto-refresh preferences

## Keyboard Shortcuts

- **Double-click**: Open MR in browser
- **Right-click**: Show context menu with additional options

## Enhanced Error Handling

The application now provides detailed error messages and troubleshooting guidance for common issues:

### Connection Testing
- **Test Button**: Test your connection without saving credentials
- **Connection Help**: Detailed help dialog with step-by-step guidance
- **Real-time Validation**: Input fields are validated as you type

### Detailed Error Messages
The application provides specific error messages for:
- **Authentication Issues**: Invalid or expired tokens
- **Network Problems**: DNS resolution, timeouts, connection refused
- **Permission Issues**: Insufficient token permissions
- **SSL Certificate Problems**: Certificate validation errors
- **Server Issues**: GitLab server errors and downtime

### Troubleshooting

#### Connection Issues
**Authentication Failed (401)**
- Your access token is incorrect or expired
- Recreate your access token with proper permissions

**Access Forbidden (403)**
- Token doesn't have required permissions
- Ensure token has 'read_api' and 'read_repository' scopes

**Cannot Resolve Hostname**
- Check your internet connection
- Verify the GitLab URL is correct (include https://)
- Try accessing the URL in your browser first

**Connection Timeout**
- GitLab server may be slow or overloaded
- Check your internet connection
- Try again in a few moments

**SSL Certificate Error**
- GitLab server has certificate issues
- For self-signed certificates, uncheck "Verify SSL certificates" option
- Contact your GitLab administrator
- For self-hosted GitLab, check certificate configuration

### SSL Configuration

The application includes an SSL verification option for compatibility with different GitLab setups:

- **✅ Keep SSL verification enabled** for:
  - Public GitLab instances (gitlab.com)
  - Production servers with valid certificates
  - Security-critical environments

- **❌ Disable SSL verification** for:
  - Self-hosted GitLab with self-signed certificates
  - Development/testing environments
  - Internal servers with certificate issues

**Security Note**: Only disable SSL verification for trusted internal servers. This makes connections less secure by not validating server certificates.

#### No Merge Requests Showing
- Check the state filter (default is "opened")
- Verify you have access to projects with merge requests
- Use the project filter to focus on specific repositories
- Try refreshing manually

#### Performance Issues
- The application fetches data for all your projects
- Large numbers of projects may take time to load
- Use project filters to focus on specific repositories
- Enable auto-refresh for background updates

## Contributing

Feel free to submit issues and enhancement requests!

## License

This project is open source and available under the MIT License.
