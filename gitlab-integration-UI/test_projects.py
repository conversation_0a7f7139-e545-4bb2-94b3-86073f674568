#!/usr/bin/env python3
"""
Test script to debug project loading issues
"""

import sys
import os
sys.path.append(os.path.dirname(__file__))

from main import GitLabAPI

def test_project_access():
    """Test project access with different approaches"""
    
    print("🔍 GitLab Project Access Debugger")
    print("=" * 50)
    
    # Get credentials
    url = input("Enter GitLab URL: ").strip()
    token = input("Enter Access Token: ").strip()
    
    if not url or not token:
        print("❌ URL and token are required")
        return
    
    print(f"\n🔗 Testing connection to: {url}")
    print("🔒 SSL verification: DISABLED (for compatibility)")
    
    # Create API instance
    api = GitLabAPI(url, token, verify_ssl=False)
    
    # Test connection first
    print("\n1️⃣ Testing basic connection...")
    success, message = api.test_connection()
    if not success:
        print(f"❌ Connection failed: {message}")
        return
    print(f"✅ Connection successful: {message}")
    
    # Test API access
    print("\n2️⃣ Testing API access...")
    api.test_api_access()
    
    # Test project loading
    print("\n3️⃣ Testing project loading...")
    projects = api.get_user_projects()
    
    if projects:
        print(f"\n✅ SUCCESS: Found {len(projects)} projects!")
        print("\n📋 Project List:")
        for i, project in enumerate(projects[:10]):  # Show first 10
            print(f"  {i+1:2d}. {project.get('name', 'Unknown')} (ID: {project.get('id', 'Unknown')})")
            print(f"      Path: {project.get('path_with_namespace', 'Unknown')}")
            print(f"      Visibility: {project.get('visibility', 'Unknown')}")
            print()
        
        if len(projects) > 10:
            print(f"  ... and {len(projects) - 10} more projects")
    else:
        print("\n❌ No projects found!")
        print("\n🔧 Troubleshooting suggestions:")
        print("1. Check if your token has 'read_api' scope")
        print("2. Verify you have access to at least one GitLab project")
        print("3. Try creating a new access token with full permissions")
        print("4. Check if your GitLab instance requires specific permissions")
    
    print("\n" + "=" * 50)
    print("🐛 Debug test completed!")

if __name__ == "__main__":
    try:
        test_project_access()
    except KeyboardInterrupt:
        print("\n❌ Test interrupted by user")
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
