#!/usr/bin/env python3
"""
Test script to verify all imports work correctly
"""

try:
    import tkinter as tk
    print("✓ tkinter imported successfully")
except ImportError as e:
    print(f"✗ tkinter import failed: {e}")

try:
    from tkinter import ttk, messagebox, simpledialog
    print("✓ tkinter submodules imported successfully")
except ImportError as e:
    print(f"✗ tkinter submodules import failed: {e}")

try:
    import requests
    print("✓ requests imported successfully")
except ImportError as e:
    print(f"✗ requests import failed: {e}")
    print("Please install requests: pip install requests")

try:
    import json
    print("✓ json imported successfully")
except ImportError as e:
    print(f"✗ json import failed: {e}")

try:
    from datetime import datetime
    print("✓ datetime imported successfully")
except ImportError as e:
    print(f"✗ datetime import failed: {e}")

try:
    import threading
    print("✓ threading imported successfully")
except ImportError as e:
    print(f"✗ threading import failed: {e}")

try:
    import time
    print("✓ time imported successfully")
except ImportError as e:
    print(f"✗ time import failed: {e}")

try:
    from typing import List, Dict, Optional
    print("✓ typing imported successfully")
except ImportError as e:
    print(f"✗ typing import failed: {e}")

try:
    import webbrowser
    print("✓ webbrowser imported successfully")
except ImportError as e:
    print(f"✗ webbrowser import failed: {e}")

try:
    import os
    print("✓ os imported successfully")
except ImportError as e:
    print(f"✗ os import failed: {e}")

try:
    from dataclasses import dataclass
    print("✓ dataclasses imported successfully")
except ImportError as e:
    print(f"✗ dataclasses import failed: {e}")

print("\nAll imports tested!")
print("If all imports show ✓, you can run the main application with: python main.py")
