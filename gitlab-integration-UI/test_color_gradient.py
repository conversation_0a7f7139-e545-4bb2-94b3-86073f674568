#!/usr/bin/env python3
"""
Test script to verify the age-based color gradient functionality
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import datetime, timedelta
from main import GitLabMRMonitor

def test_color_gradient():
    """Test the age-based color gradient"""
    
    # Create a mock GitLab monitor instance to access the color methods
    monitor = GitLabMRMonitor()
    
    print("🎨 Testing Age-Based Color Gradient")
    print("=" * 50)
    
    # Test different ages
    test_cases = [
        ("Today", 0),
        ("3 days ago", 3),
        ("1 week ago", 7),
        ("2 weeks ago", 14),
        ("3 weeks ago", 21),
        ("1 month ago", 30),
        ("2 months ago", 60),
    ]
    
    for description, days_ago in test_cases:
        # Create a test date
        test_date = datetime.now() - timedelta(days=days_ago)
        test_date_str = test_date.isoformat() + 'Z'
        
        # Get colors
        text_color, bg_color = monitor.get_age_based_colors(test_date_str)
        
        print(f"{description:15} ({days_ago:2d} days): Text={text_color}, Background={bg_color}")
    
    print("\n🎯 Color Gradient Explanation:")
    print("- MRs less than 7 days old: Black text on white background")
    print("- MRs 7-30 days old: Gradient from dark green to dark red text")
    print("- Background: Gradient from light green to light red")
    print("- Older than 30 days: Dark red text on light red background")

if __name__ == "__main__":
    test_color_gradient()
