
import os
import yaml
import sys
import re

def check_for_value_in_yml(directory):
    """
    Checks every YAML file in the specified directory for the 'for:' value
    to be less than or equal to 30 minutes, and reports the alert name and file path.
    """
    issues_found = False
    
    for root, _, files in os.walk(directory):
        for file_name in files:
            if file_name.endswith(('.yml', '.yaml')):
                file_path = os.path.join(root, file_name)
                print(f"Checking file: {file_path}")
                
                try:
                    with open(file_path, 'r') as f:
                        data = yaml.safe_load(f)
                        
                        # --- Specific handling for Prometheus alert rule file format ---
                        # Prometheus alert rule files typically have a 'groups' key at the top level,
                        # and each group contains a list of 'rules'.
                        if isinstance(data, dict) and 'groups' in data:
                            for group in data.get('groups', []): # Use .get to safely access 'groups'
                                if isinstance(group, dict) and 'rules' in group:
                                    for rule in group.get('rules', []): # Use .get to safely access 'rules'
                                        # Check if the rule is a dictionary and contains 'alert' and 'for' keys
                                        if isinstance(rule, dict) and 'alert' in rule and 'for' in rule:
                                            alert_name = rule['alert']
                                            for_value = rule['for']
                                            if not is_duration_less_than_or_equal_to_30m(for_value):
                                                print(f"  🚨 \033[31mIssue: Alert\033[0m  \033[33m'{alert_name}'\033[0m in \033[1;36m{file_path} has\033[0m \033[31m'for:' value '{for_value}' which is greater than 30m.\033[0m")
                                                issues_found = True                            
                except yaml.YAMLError as e:
                    print(f"  ❌ Error parsing YAML file {file_path}: {e}")
                    issues_found = True
                except Exception as e:
                    print(f"  ❌ An unexpected error occurred with file {file_path}: {e}")
                    issues_found = True

    if not issues_found:
        print("\n✅ All 'for:' values are 30m or less in all rules YAML files.")
    else:
        sys.exit(1)

# Helper functions (parse_duration_to_minutes and is_duration_less_than_or_equal_to_30m)
# remain the same as in the previous response.

def parse_duration_to_minutes(duration_str):
    """
    Parses a duration string (e.g., '5m', '1h', '2h30m') into total minutes.
    Returns None if the format is invalid.
    """
    total_minutes = 0
    
    # Regular expression to find numbers followed by 'h' or 'm'
    matches = re.findall(r'(\d+)([mh])', duration_str)
    
    if not matches:
        return None # No valid duration components found

    for value, unit in matches:
        value = int(value)
        if unit == 'h':
            total_minutes += value * 60
        elif unit == 'm':
            total_minutes += value
            
    return total_minutes

def is_duration_less_than_or_equal_to_30m(duration_str):
    """
    Checks if a given duration string is less than or equal to 30 minutes.
    Assumes 'm' for minutes and 'h' for hours.
    """
    if not isinstance(duration_str, str):
        return True # Not a string, so not a duration to check
        
    minutes = parse_duration_to_minutes(duration_str)
    
    if minutes is None:
        # print(f"    ⚠️ Warning: Could not parse duration '{duration_str}'. Assuming it does not violate the condition.")
        return True # Cannot parse, so we don't flag it as an issue
        
    return minutes <= 30

if __name__ == "__main__":
    # Specify the directory to check
    target_directory = 'rules' 
    # Or specify a different directory:
    # target_directory = '/path/to/your/yaml/files'

    if not os.path.isdir(target_directory):
        print(f"Error: Directory '{target_directory}' not found.")
    else:
        check_for_value_in_yml(target_directory)