## Proposal for 'PD-Route-Checker' stage name group or that these are grouped under one stage 
# stage: pagerduty-route-checker. - was unit-tests
.route-checker-template: &route-checker-template
  stage: pagerduty-route-checker
  image: python:3.11-slim
  allow_failure: true
  needs: []
PD-service-route-checker-prod:
  <<: *route-checker-template
  script:
    - python tools/pd-service-route-checker/directory-route-checker.py -f rules/prod/

PD-service-route-checker-global:
  <<: *route-checker-template
  script:
    - python tools/pd-service-route-checker/directory-route-checker.py -f rules/global/

PD-service-route-checker-ops:
  <<: *route-checker-template
  script:
    - python tools/pd-service-route-checker/directory-route-checker.py -f rules/ops/