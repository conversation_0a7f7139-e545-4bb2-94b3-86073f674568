## Proposal for 'Alert-Validation' stage name group or that these are grouped under one stage 
# stage : alert-validation-scripts - was unit-tests
.alert-validation-template: &alert-validation-template
  stage: alert-validation-scripts
  image: python:3.11-slim
  needs: []

Alert-duplicate-name-validation:
  <<: *alert-validation-template
  allow_failure: true
  script:
    - python -m pip install pyyaml
    - python3 tools/duplicate-alert-checker/duplicate_alert_checker.py


Alert-for-value-validation:
  <<: *alert-validation-template
  allow_failure: true
  script:
    - python -m pip install pyyaml
    - python3 tools/duplicate-alert-checker/prometheus_alert_validations.py