### Syntax and offline linting validations ###
## Proposal for 'Template-Syntax-Validation' stage name group or that these are grouped 
#stage: templating-and-syntax-validations
.pint-validation-template: &pint-validation-template
  stage: templating-and-syntax-validations
  image:  us-docker.pkg.dev/xdr-registry-prod-us-01/golden-images/pint:0.71.8
  allow_failure: true
  needs: []

pint-offline-unit-test-prod-rules:
  <<: *pint-validation-template
  script:
    - pint --offline -c pint-config/pint-config.hcl lint rules/prod/* -w Fatal

pint-offline-unit-test-global-rules:
  <<: *pint-validation-template
  script:
    - pint --offline -c pint-config/pint-config.hcl lint rules/global/* -w Fatal

pint-ci-branch-syntax-validation:
  stage: templating-and-syntax-validations
  image:  ghcr.io/cloudflare/pint:0.71.8
  allow_failure: false
  needs: []
  script:
    - pint -c pint-config/pint-ci-config.hcl ci