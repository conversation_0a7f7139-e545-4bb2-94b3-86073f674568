#!/usr/bin/env python3
"""
Check metric names against one or more JSON reports and print results with colors:
- Found metrics in green, along with their cardinality block
- Not found but similar names in yellow (suggestions shown in cyan)
- Not found at all in red

Usage examples:
  # Multiple JSONs (globs supported, quoted or unquoted)
  python3 metrics_checker/check_metrics.py --jsons full_report*.json another.json --metrics foo bar baz
  python3 metrics_checker/check_metrics.py --jsons reports/*.json --metrics-file metrics.txt

The JSON is expected to be a list of objects, each with at least:
  { "name": "metric_name", "cardinality": { ... }, ... }
"""

import argparse
import json
import os
import sys
import glob
from difflib import SequenceMatcher
from typing import Dict, List, Tuple

ANSI_GREEN = "\033[92m"
ANSI_YELLOW = "\033[93m"
ANSI_RED = "\033[91m"
ANSI_CYAN = "\033[96m"
ANSI_RESET = "\033[0m"
ANSI_BOLD = "\033[1m"

# Labels to exclude from display (non-core labels preference)
EXCLUDED_LABELS = {
    'kubernetes_namespace',
    'tenant_type',
    'product_type',
    'product_tier',
    'xdr_id',
    'namespace',
    'lcaas_id',
}


def load_reports(json_paths: List[str]) -> Tuple[Dict[str, dict], List[str]]:
    """Load one or more JSON reports and merge entries by metric name.
    If a metric appears in multiple files, the first occurrence wins unless a later
    occurrence has a 'cardinality' block while the first does not, in which case we keep
    the one with cardinality.
    Supports glob patterns (e.g., reports/*.json) whether or not the shell expands them.
    """
    name_to_entry: Dict[str, dict] = {}
    names_in_order: List[str] = []

    # Expand any glob patterns to actual file paths
    files_to_read: List[str] = []
    for pattern in json_paths:
        matches = glob.glob(pattern)
        if matches:
            files_to_read.extend(matches)
        elif os.path.exists(pattern):
            files_to_read.append(pattern)
        # else: silently ignore missing pattern; we'll error if none found overall

    if not files_to_read:
        raise FileNotFoundError(
            f"No JSON files found for patterns: {', '.join(json_paths)}"
        )

    for json_path in files_to_read:
        with open(json_path, 'r') as f:
            data = json.load(f)
        if not isinstance(data, list):
            raise ValueError(f"Expected JSON to be a list of metric objects: {json_path}")
        for entry in data:
            name = entry.get('name')
            if not name:
                continue
            if name not in name_to_entry:
                name_to_entry[name] = entry
                names_in_order.append(name)
            else:
                # Prefer an entry that contains 'cardinality' if existing one doesn't
                has_card_new = bool(entry.get('cardinality'))
                has_card_old = bool(name_to_entry[name].get('cardinality'))
                if has_card_new and not has_card_old:
                    name_to_entry[name] = entry
    return name_to_entry, names_in_order


def read_metrics_from_file(path: str) -> List[str]:
    with open(path, 'r') as f:
        return [line.strip() for line in f if line.strip()]


def compute_similarity(a: str, b: str) -> float:
    # Combine ratio with simple prefix/contain heuristics
    a_low, b_low = a.lower(), b.lower()
    ratio = SequenceMatcher(None, a_low, b_low).ratio()
    if a_low in b_low or b_low in a_low:
        ratio = max(ratio, 0.85)
    # underscore-aware: boost if initial segments match
    a_parts, b_parts = a_low.split('_'), b_low.split('_')
    common = 0
    for x, y in zip(a_parts, b_parts):
        if x == y:
            common += 1
        else:
            break
    if common >= 1:
        ratio = max(ratio, 0.7 + min(0.2, common * 0.05))
    return ratio


def find_similar(name: str, candidates: List[str], threshold: float = 0.8, max_suggestions: int = 5) -> List[Tuple[str, float]]:
    scores = []
    for cand in candidates:
        if cand == name:
            continue
        s = compute_similarity(name, cand)
        if s > threshold:  # strictly greater than threshold
            scores.append((cand, s))
    scores.sort(key=lambda x: x[1], reverse=True)
    return scores[:max_suggestions]


def color_text(text: str, color: str, bold: bool = False) -> str:
    if bold:
        return f"{ANSI_BOLD}{color}{text}{ANSI_RESET}"
    return f"{color}{text}{ANSI_RESET}"


def print_non_core_labels(entry: dict) -> None:
    """Print labels excluding a predefined set of common labels."""
    labels = entry.get('labels') or {}
    filtered = {k: v for k, v in labels.items() if k not in EXCLUDED_LABELS}
    if not filtered:
        print(color_text("  labels (non-core): none", ANSI_YELLOW))
        return
    print(color_text("  labels (non-core):", ANSI_YELLOW))
    for key, vals in filtered.items():
        if isinstance(vals, list):
            display_vals = vals
        elif vals is None:
            display_vals = []
        else:
            display_vals = [vals]
        if len(display_vals) <= 5:
            joined = ", ".join(str(x) for x in display_vals)
            print(color_text(f"    - {key}: [{joined}]", ANSI_YELLOW))
        else:
            preview = ", ".join(str(x) for x in display_vals[:5])
            print(color_text(f"    - {key}: [{preview}, ...] ({len(display_vals)} values)", ANSI_YELLOW))


def print_cardinality(entry: dict) -> None:
    card = entry.get('cardinality')
    if card is None:
        print("  cardinality: (not available)")
        return
    print("  cardinality:")
    for k in ['per_pod', 'current', 'min', 'max', 'avg']:
        if k in card:
            print(f"    - {k}: {card[k]}")


def main():
    parser = argparse.ArgumentParser(
        description="Cross-check metric names against one or more JSON reports"
    )
    parser.add_argument(
        '--json', '--jsons', dest='jsons', nargs='+', required=True,
        help='Path(s) to JSON report(s)'
    )
    group = parser.add_mutually_exclusive_group(required=True)
    group.add_argument('--metrics', nargs='+', help='Metric names to check (space-separated)')
    group.add_argument('--metrics-file', help='Path to a file containing metric names (one per line)')
    parser.add_argument(
        '--threshold', type=float, default=0.8,
        help='Similarity threshold (suggestions must be strictly greater than this; default: 0.8)'
    )
    parser.add_argument('--max-suggestions', type=int, default=5, help='Max similar names to show (default: 5)')
    args = parser.parse_args()

    name_to_entry, all_names = load_reports(args.jsons)

    if args.metrics:
        input_metrics = args.metrics
    else:
        input_metrics = read_metrics_from_file(args.metrics_file)

    found = []  # (name, entry)
    similar = []  # (name, [(cand, score), ...])
    not_found = []  # [name]

    name_set = set(all_names)

    for m in input_metrics:
        if m in name_set:
            found.append((m, name_to_entry[m]))
        else:
            suggestions = find_similar(m, all_names, threshold=args.threshold, max_suggestions=args.max_suggestions)
            if suggestions:
                similar.append((m, suggestions))
            else:
                not_found.append(m)

    # Output
    if found:
        print(color_text(f"\nFound metrics ({len(found)}):", ANSI_GREEN, bold=True))
        for m, entry in found:
            print(color_text(f"- {m}", ANSI_GREEN))
            print_cardinality(entry)
            print_non_core_labels(entry)
    else:
        print(color_text("\nFound metrics (0):", ANSI_GREEN, bold=True))

    if similar:
        print(color_text(f"\nNot found but similar names ({len(similar)}):", ANSI_YELLOW, bold=True))
        for m, suggestions in similar:
            print(color_text(f"- {m}", ANSI_YELLOW))
            for cand, score in suggestions:
                print(color_text(f"    -> {cand}", ANSI_CYAN))
    else:
        print(color_text("\nNot found but similar names (0):", ANSI_YELLOW, bold=True))

    if not_found:
        print(color_text(f"\nNot found at all ({len(not_found)}):", ANSI_RED, bold=True))
        for m in not_found:
            print(color_text(f"- {m}", ANSI_RED))
    else:
        print(color_text("\nNot found at all (0):", ANSI_RED, bold=True))


if __name__ == '__main__':
    try:
        main()
    except Exception as e:
        print(color_text(f"Error: {e}", ANSI_RED), file=sys.stderr)
        sys.exit(1)
