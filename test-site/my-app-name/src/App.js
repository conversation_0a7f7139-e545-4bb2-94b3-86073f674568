import React, { useEffect, useMemo, useRef, useState } from 'react';
import './App.css';

// Simple search engine powered by Wikipedia's public search API (no API key needed)
// You can swap the provider later (e.g., Google CSE, Bing, Algolia) by changing fetchSearchResults.

function stripHtml(html) {
  const div = document.createElement('div');
  div.innerHTML = html;
  return div.textContent || div.innerText || '';
}

async function fetchSearchResults(query, abortSignal) {
  // Wikipedia REST search API
  const url = `https://en.wikipedia.org/w/rest.php/v1/search/page?q=${encodeURIComponent(query)}&limit=10`;
  const res = await fetch(url, { signal: abortSignal, headers: { 'Accept': 'application/json' } });
  if (!res.ok) throw new Error(`Search failed (${res.status})`);
  const json = await res.json();
  const pages = json?.pages || [];
  return pages.map(p => ({
    id: p.id,
    title: p.title,
    description: p.description || '',
    snippet: stripHtml(p.excerpt || ''),
    url: `https://en.wikipedia.org/wiki/${encodeURIComponent(p.key || p.title)}`,
    thumb: p.thumbnail?.url || null,
  }));
}

function App() {
  const [query, setQuery] = useState('');
  const [results, setResults] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const controllerRef = useRef(null);

  const canSearch = useMemo(() => query.trim().length > 1, [query]);

  const runSearch = async (q) => {
    if (!q || !q.trim()) return;
    if (controllerRef.current) controllerRef.current.abort();
    const controller = new AbortController();
    controllerRef.current = controller;
    setLoading(true);
    setError('');
    try {
      const data = await fetchSearchResults(q.trim(), controller.signal);
      setResults(data);
    } catch (e) {
      if (e.name !== 'AbortError') setError(e.message || 'Search failed');
    } finally {
      setLoading(false);
    }
  };

  const onSubmit = (e) => {
    e.preventDefault();
    runSearch(query);
  };

  // Optional: search when pressing Enter inside the input
  const onKeyDown = (e) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      runSearch(query);
    }
  };

  useEffect(() => {
    return () => controllerRef.current?.abort();
  }, []);

  return (
    <div className="App">
      <div className="search-container">
        <h1 className="search-title">Search</h1>
        <form onSubmit={onSubmit} className="search-form">
          <input
            type="text"
            placeholder="Search the web (powered by Wikipedia)"
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            onKeyDown={onKeyDown}
            className="search-input"
          />
          <button type="submit" disabled={!canSearch || loading} className="search-button">
            {loading ? 'Searching…' : 'Search'}
          </button>
        </form>

        {error && (
          <div className="error-message">Error: {error}</div>
        )}

        {!loading && results.length === 0 && !error && (
          <div className="no-results">
            Try searching for something like "OpenAI" or "React".
          </div>
        )}

        <ul className="results-list">
          {results.map((r) => (
            <li key={r.id} className="result-item">
              <a href={r.url} target="_blank" rel="noreferrer" className="result-title">
                {r.title}
              </a>
              {r.description && (
                <div className="result-description">{r.description}</div>
              )}
              {r.snippet && (
                <div className="result-snippet">{r.snippet}</div>
              )}
            </li>
          ))}
        </ul>
      </div>
    </div>
  );
}

export default App;
