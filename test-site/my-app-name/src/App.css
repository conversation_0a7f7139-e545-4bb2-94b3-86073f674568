/* Modern gradient search engine styling */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen',
    'Ubuntu', 'Can<PERSON>ell', '<PERSON><PERSON> Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: #f8f9fa;
  min-height: 100vh;
}

.App {
  min-height: 100vh;
  background: #ffffff;
}

.search-container {
  padding: 40px 24px;
  max-width: 900px;
  margin: 0 auto;
}

.search-title {
  font-size: 3.5rem;
  font-weight: 700;
  margin-bottom: 2rem;
  text-align: center;
  color: #2c3e50;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.search-form {
  display: flex;
  gap: 12px;
  margin-bottom: 2rem;
  align-items: stretch;
}

.search-input {
  flex: 1;
  padding: 16px 20px;
  font-size: 16px;
  border: 2px solid #e9ecef;
  border-radius: 50px;
  background: #ffffff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  outline: none;
}

.search-input:focus {
  border-color: #007bff;
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.15);
  transform: translateY(-1px);
}

.search-input::placeholder {
  color: rgba(0, 0, 0, 0.5);
}

.search-button {
  padding: 16px 24px;
  font-size: 16px;
  font-weight: 600;
  border: none;
  border-radius: 50px;
  background: #007bff;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
  min-width: 120px;
}

.search-button:hover:not(:disabled) {
  background: #0056b3;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.4);
}

.search-button:active {
  transform: translateY(0);
}

.search-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.error-message {
  color: #ff6b6b;
  background: rgba(255, 107, 107, 0.1);
  padding: 12px 20px;
  border-radius: 12px;
  margin-top: 16px;
  text-align: center;
  font-weight: 500;
}

.no-results {
  color: #6c757d;
  text-align: center;
  margin-top: 2rem;
  font-size: 18px;
  background: #f8f9fa;
  padding: 20px;
  border-radius: 16px;
  border: 1px solid #e9ecef;
}

.results-list {
  list-style: none;
  padding: 0;
  margin-top: 2rem;
}

.result-item {
  background: #ffffff;
  margin-bottom: 16px;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 1px solid #e9ecef;
}

.result-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  border-color: #007bff;
}

.result-title {
  font-size: 20px;
  font-weight: 600;
  color: #007bff;
  text-decoration: none;
  display: block;
  margin-bottom: 8px;
  transition: all 0.3s ease;
}

.result-title:hover {
  color: #0056b3;
  text-decoration: underline;
}

.result-description {
  color: #666;
  margin-bottom: 8px;
  font-weight: 500;
  line-height: 1.4;
}

.result-snippet {
  color: #555;
  line-height: 1.5;
  font-size: 14px;
}

/* Loading animation */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.search-button:disabled {
  animation: pulse 1.5s ease-in-out infinite;
}

/* Responsive design */
@media (max-width: 768px) {
  .search-container {
    padding: 20px 16px;
  }

  .search-title {
    font-size: 2.5rem;
    margin-bottom: 1.5rem;
  }

  .search-form {
    flex-direction: column;
    gap: 12px;
  }

  .search-input, .search-button {
    width: 100%;
  }

  .result-item {
    padding: 20px;
  }
}
