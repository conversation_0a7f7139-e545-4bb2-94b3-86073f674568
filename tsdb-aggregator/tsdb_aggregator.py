import time
import requests
import json
import random
import sys
from prometheus_client import start_http_server, Gauge

if len(sys.argv) > 2:
    if sys.argv[1] == '-overload' or sys.argv[1] == '--abuse-memory':
        FLAG = sys.argv[2]


# --- Configuration ---
PROMETHEUS_URL = 'http://localhost:9090'
LCAAS_ID = '9997464373539'
EXPORTER_PORT = 8088
COLLECTION_INTERVAL_SECONDS = 60         # How often to fetch new data

# -- Filtering Configuration --
SERIES_COUNT_THRESHOLD = 0
MEMORY_USAGE_THRESHOLD = 0

TSDB_API_ENDPOINT = f'{PROMETHEUS_URL}/api/v1/status/tsdb'

ABUSE_ACTION_VALUES = [
        "GET",
        "Get",
        "GetAuthSettings",
        "GetBackupConfiguration",
        "GetConfiguration",
        "GetEffectiveRouteTable",
        "GetInSubscription",
        "GetInstanceView",
        "GetNetworkRuleSet",
        "GetPrivateEndpointConnectionList",
        "GetWebAppStacks",
        "InstanceView",
        "List",
        "ListAll",
        "ListApplicationSettings",
        "ListAtSubscriptionLevel",
        "ListAuthorizationRules",
        "ListBasicPublishingCredentialsPolicies",
        "ListByAutomationAccount",
        "ListByDatabase",
        "ListByEventHub",
        "ListByGallery",
        "ListByGalleryImage",
        "ListByNamespace",
        "ListByResourceGroup",
        "ListByServer",
        "ListBySubscription",
        "ListBySubscriptionId",
        "ListByVault",
        "ListByWorkspace",
        "ListConfigurations",
        "ListEffectiveNetworkSecurityGroups",
        "ListForSubscription",
        "ListFunctions",
        "ListKeys",
        "ListLocations",
        "ListMetadata",
        "ListModels",
        "ListMoveCollectionsBySubscription",
        "ListPolicyForTenant",
        "ListSlots",
        "ListVirtualMachineScaleSetNetworkInterfaces",
        "ListVirtualMachineScaleSetPublicIPAddresses"
      ]
ABUSE_LABEL_VALUES = []
ABUSE_SERVICE_VALUES = [
        "Microsoft.KeyVault",
        "Microsoft.Network",
        "Microsoft.Security",
        "Microsoft.Compute",
        "Microsoft.Sql",
        "Microsoft.Resources",
        "Microsoft.Purview",
        "Microsoft.Web",
        "Microsoft.DataProtection",
        "Microsoft.EventHub",
        "Microsoft.ApiManagement",
        "Microsoft.NetworkFunction",
        "Microsoft.RecoveryServices",
        "Microsoft.Kusto",
        "Microsoft.ServiceFabric",
        "Microsoft.AppConfiguration",
        "Microsoft.MachineLearningServices",
        "Microsoft.Insights",
        "Microsoft.DomainRegistration",
        "Microsoft.Chaos",
        "Microsoft.DesktopVirtualization",
        "Microsoft.ManagedServices",
        "Microsoft.ServiceBus",
        "Microsoft.Cdn",
        "Microsoft.Cache",
        "Microsoft.ContainerRegistry",
        "Microsoft.AnalysisServices",
        "Microsoft.DBforPostgreSQL",
        "Microsoft.Storage",
        "Microsoft.OperationalInsights",
        "Microsoft.Synapse",
        "Microsoft.CognitiveServices",
        "Microsoft.VideoIndexer",
        "Microsoft.Automation",
        "Microsoft.Batch",
        "Microsoft.HealthBot",
        "Microsoft.StorageCache",
        "Microsoft.PowerBIdedicated",
        "Microsoft.HDInsight",
        "Microsoft.ContainerInstance",
        "Microsoft.DigitalTwins",
        "Microsoft.DataFactory",
        "Microsoft.Dashboard",
        "Microsoft.Elastic",
        "Microsoft.RedHatOpenShift",
        "Microsoft.NetApp",
        "Microsoft.DBforMySQL",
        "Microsoft.AppPlatform",
        "Microsoft.DataMigration",
        "Microsoft.Relay",
        "Microsoft.Authorization",
        "Microsoft.DBforMariaDB",
        "Microsoft.AlertsManagement",
        "Microsoft.SqlVirtualMachine",
        "Microsoft.DocumentDB",
        "Microsoft.ContainerService",
        "Microsoft.StreamAnalytics",
        "Microsoft.Attestation",
        "Microsoft.Blueprint",
        "Microsoft.BotService",
        "Microsoft.Management",
        "Microsoft.Logic",
        "Microsoft.Datadog",
        "Microsoft.NotificationHubs",
        "Microsoft.VisualStudio",
        "Microsoft.Easm",
        "Microsoft.DevTestLab",
        "Microsoft.Confluent",
        "Microsoft.AzureActiveDirectory",
        "Microsoft.AzureStackHCI",
        "Microsoft.DataBoxEdge",
        "Microsoft.EventGrid",
        "Microsoft.Search",
        "Microsoft.Databricks",
        "Microsoft.Maps",
        "Microsoft.Automanage",
        "Microsoft.Devices",
        "Microsoft.LabServices",
        "Microsoft.LoadTestService",
        "Microsoft.StorageSync",
        "Microsoft.DevCenter",
        "Microsoft.SignalRService",
        "Microsoft.ManagedIdentity",
        "Microsoft.Quantum",
        "Microsoft.Solutions",
        "Microsoft.IoTCentral",
        "Microsoft.Advisor",
        "Microsoft.App",
        "Microsoft.ConfidentialLedger",
        "Microsoft.HybridCompute",
        "Microsoft.StorageMover",
        "Microsoft.DevOps",
        "Microsoft.Communication",
        "Microsoft.Migrate",
        "Microsoft.Subscription"
      ]
# 1. Define the Gauge metric GLOBALLY.
#    This object will persist and be updated by our collection loop.
STATS_GAUGE = Gauge(
    'stat_series_count',
    'Series count for each metric name from TSDB stats.',
    ['metricname', 'lcaas_id']
)

MEMORY_GAUGE = Gauge(
    'memory_usage_per_metric',
    'Series count for each metric name from TSDB stats.',
    ['metricname', 'lcaas_id']
)

QUERY_ENDPOINT = f"{PROMETHEUS_URL}/api/v1/query"
TOTAL_SERIES_QUERY = 'sum(count({__name__=~".+"}) by (__name__))'


def get_total_series():
    response = requests.get(QUERY_ENDPOINT,
                            params={'query': TOTAL_SERIES_QUERY}
                            )
    return response.json()['data']['result'][0]['value'][1]


MEMORY_ABUSE_GAUGE = Gauge(
        'memory_abuse_metricsa',
        'Series count for each metric name from TSDB stats.',
        ['metricname', 'lcaas_id', 'action' , 'service']
        )


def abuse_memory():
    """
    Fetches data and updates the pre-defined Prometheus Gauge.
    This function can be called repeatedly without causing an error.
    """
    # --- Mock Dependencies (for a runnable example) ---
    # Replace these with your actual values and functions
    lcaas_id = '9997464373539'
    
    def get_mock_tsdb_data():
        """Mocks the response from your TSDB_API_ENDPOINT."""
        return {
            'data': {
                'seriesCountByMetricName': [
                    {'name': 'http_requests_total', 'value': 5000},
                    {'name': 'failed_logins_total', 'value': 250},
                ]
            }
        }

    def get_total_series():
        """Mocks the function to get total series count."""
        return 10000

    # In your real code, you would make the actual request here:
    # response = requests.get(TSDB_API_ENDPOINT, timeout=10)
    # response.raise_for_status()
    # tsdb_data = response.json()
    tsdb_data = get_mock_tsdb_data() # Using mocked data

    total_series = get_total_series()
    series_counts = tsdb_data.get('data', {}).get('seriesCountByMetricName', [])
    
    print("Updating metrics...")
    for item in series_counts:
        metric_name = item.get('name')
        series_count = item.get('value')

        if metric_name is None or series_count is None:
            continue

        memory_usage_percent = (series_count / int(total_series)) * 100

        # This loop correctly creates a unique time series for each action
        for action_label in ABUSE_SERVICE_VALUES:
            for service_label in ABUSE_SERVICE_VALUES:
                random_number = random.randint(1, 1000000)
            # This USES the global MEMORY_ABUSE_GAUGE, it does not create it.
                MEMORY_ABUSE_GAUGE.labels(
                    metricname=metric_name,
                    lcaas_id=lcaas_id,
                    action=action_label,
                    service=f'{service_label}' + f'_{random_number}'
                    ).set(memory_usage_percent)
                collect_and_update_metrics()

    


def collect_and_update_metrics():
    """Fetches stats and updates the global GAUGE metric."""
    print("Collecting metrics from TSDB...")
    try:
        # Fetch data from Prometheus API
        response = requests.get(TSDB_API_ENDPOINT, timeout=10)
        response.raise_for_status()
        tsdb_data = response.json()

        if tsdb_data.get('status') != 'success':
            print("API response status was not 'success'.")
            return
        total_series = get_total_series()
        series_counts = tsdb_data.get('data', {}).get('seriesCountByMetricName', [])
        # This will hold the labels of metrics we see in this scrape
        seen_metrics = set()
        # Update gauge for metrics that meet the criteria
        for item in series_counts:
            metric_name = item.get('name')
            series_count = item.get('value')
            memory_usage = (series_count / int(total_series))*100
            if metric_name is None or series_count is None:
                continue

            # Add the label combo to our set of seen metrics
            seen_metrics.add((metric_name, LCAAS_ID))
            if memory_usage > MEMORY_USAGE_THRESHOLD:
                MEMORY_GAUGE.labels(metricname=metric_name, lcaas_id=LCAAS_ID).set(memory_usage)
            # Apply the filtering logic
            if (series_count > SERIES_COUNT_THRESHOLD):
                STATS_GAUGE.labels(metricname=metric_name, lcaas_id=LCAAS_ID).set(series_count)
            else:
                # If the metric is being filtered, we ensure it's not exposed
                # by trying to remove it. This prevents showing a stale value.
                STATS_GAUGE.remove(metric_name, LCAAS_ID)

    except requests.exceptions.RequestException as e:
        print(f"Error during collection: {e}")

if __name__ == '__main__':
    # 2. Start an HTTP server to expose the metrics on the specified port.
    #    This runs in a background thread.
    start_http_server(EXPORTER_PORT)
    print(f"Exporter started on port {EXPORTER_PORT}")
    if len(sys.argv) > 1:
        if sys.argv[1] == '-overload' or sys.argv[1] == '--abuse':
            while True:
                print("Abusing memory...")
                abuse_memory()
                time.sleep(COLLECTION_INTERVAL_SECONDS)
    # 3. Run the collection loop forever.
    while True:
        collect_and_update_metrics()
        print(f"Collection finished. Sleeping for {COLLECTION_INTERVAL_SECONDS} seconds.")
        time.sleep(COLLECTION_INTERVAL_SECONDS)