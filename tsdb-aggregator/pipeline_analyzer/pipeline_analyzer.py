import requests
import time
import sys

import pprint as p
# --- Configuration ---

PROMETHEUS_URL = 'http://localhost:8080'

COLLECTION_INTERVAL_SECONDS = 60  
QUERY_ENDPOINT = f"{PROMETHEUS_URL}/api/v1/query"

## Flow --> Subscription --> pipeline/xql pods - Scylla/Cronus -> stitched sub --> dms pods --> done 

"""
Query Enumeration -- 
subscriptions : 
    - pipeline consumes from -edr
    - xql- consumes from lavawall
    - dms- consumes form stitched
    - Story builder - publishes to stitched
deployments :
    - pipeline
    - xql
    - dms
    - cronus
    - scylla
    - stitched
    - xcloud scylla
"""

subscriptions = [
    'pipeline',
    'xql',
    'dms',
    'storybuilder',
    'dms'
]

deployments = [
    'pipeline',
    'xql',
    'dms',
    'cronus',
    'scylla',
    'xcloud-scylla',
    'redis'
]

def get_query_result(query):
    """
    Generic function to query Prometheus and return the query result.
    """
    response = requests.get(QUERY_ENDPOINT,
                            params={'query': query }
                            )
    return response.json()

def query_subscription_oldest_unacked_message_age(sub , tenant):
    if env == 'st':
        if sub == 'pipeline':
            query = 'stackdriver_pubsub_subscription_pubsub_googleapis_com_subscription_oldest_unacked_message_age{subscription_id=~"edr-raw-(pipeline|replica)-.*"}'
        if sub == 'xql':
            query = 'stackdriver_pubsub_subscription_pubsub_googleapis_com_subscription_oldest_unacked_message_age{subscription_id=~"lava-wall-.*"}'
        if sub == 'storybuilder':
            query_condition = f'GnzStoryBuilder_PartitionConsumer_consumed_insert_ts'
            query = f"time() - min(max({query_condition}/1000) by (quantum))"
        if sub == 'dms':
            query = 'stackdriver_pubsub_subscription_pubsub_googleapis_com_subscription_oldest_unacked_message_age{subscription_id=~"stitched-.*"}'      
    elif env == 'regional':
        if sub == 'pipeline':
            query = f'max(stackdriver_pubsub_subscription_pubsub_googleapis_com_subscription_oldest_unacked_message_age{{subscription_id=~"edr-raw-(pipeline|replica)-.*", lcaas_id="{tenant}"}}) by (lcaas_id , subscription_id)'
        if sub == 'storybuilder':
            query_condition = f'GnzStoryBuilder_PartitionConsumer_consumed_insert_ts{{lcaas_id="{tenant}"}}'
            query = f"time() - min(max({query_condition}/1000) by (quantum))"
        if sub == 'xql':
            query = f'max(stackdriver_pubsub_subscription_pubsub_googleapis_com_subscription_oldest_unacked_message_age{{subscription_id=~"lava-wall-.*", lcaas_id="{tenant}"}}) by (lcaas_id , subscription_id)'
        if sub == 'dms':
            query = f'max(stackdriver_pubsub_subscription_pubsub_googleapis_com_subscription_oldest_unacked_message_age{{subscription_id=~"stitched-.*", lcaas_id="{tenant}"}}) by (lcaas_id , subscription_id)'
    return get_query_result(query)

def query_subscription_ack_rate(sub, tenant):
    if env == 'st':
        if sub == 'pipeline':
            query = 'rate(stackdriver_pubsub_subscription_pubsub_googleapis_com_subscription_ack_message_count{subscription_id=~"edr-raw-(pipeline|replica)-.*"}[1m])'
        if sub == 'xql':
            query = 'rate(stackdriver_pubsub_subscription_pubsub_googleapis_com_subscription_ack_message_count{subscription_id=~"lava-wall-.*"}[1m])'
        if sub == 'storybuilder':
            return
        if sub == 'dms':
            query = 'rate(stackdriver_pubsub_subscription_pubsub_googleapis_com_subscription_ack_message_count{subscription_id=~"stitched-.*"}[1m])'
        if sub == 'stitched':
            query = 'rate(stackdriver_pubsub_subscription_pubsub_googleapis_com_subscription_ack_message_count{subscription_id=~"stitched-.*"}[1m])'
    elif env == 'regional':
        if sub == 'pipeline':
            query = f'stackdriver_pubsub_subscription_pubsub_googleapis_com_subscription_ack_message_count{{subscription_id=~"edr-raw-(pipeline|replica)-.*", lcaas_id="{tenant}"}}/60'
        if sub == 'xql':
            query = f'stackdriver_pubsub_subscription_pubsub_googleapis_com_subscription_ack_message_count{{subscription_id=~"lava-wall-.*", lcaas_id="{tenant}"}}/60'
        if sub == 'dms':
            query = f'stackdriver_pubsub_subscription_pubsub_googleapis_com_subscription_ack_message_count{{subscription_id=~"stitched-.*", lcaas_id="{tenant}"}}/60'
        if sub == 'storybuilder':
            return
        if sub == 'stitched':
            query = f'stackdriver_pubsub_subscription_pubsub_googleapis_com_subscription_ack_message_count{{subscription_id=~"stitched-.*", lcaas_id="{tenant}"}}/60'
    return get_query_result(query)

def get_number_of_unacked_messages(sub, tenant):
    if env == 'st':
        if sub == 'pipeline':
            query = 'stackdriver_pubsub_subscription_pubsub_googleapis_com_subscription_num_undelivered_messages{subscription_id=~"edr-raw-(pipeline|replica)-.*"}'
        if sub == 'xql':
            query = 'stackdriver_pubsub_subscription_pubsub_googleapis_com_subscription_num_undelivered_messages{subscription_id=~"lava-wall-.*"}'
        if sub == 'storybuilder':
            return
        if sub == 'dms':
            query = 'stackdriver_pubsub_subscription_pubsub_googleapis_com_subscription_num_undelivered_messages{subscription_id=~"stitched-.*"}'
    elif env == 'regional':
        if sub == 'pipeline':
            query = f'stackdriver_pubsub_subscription_pubsub_googleapis_com_subscription_num_undelivered_messages{{subscription_id=~"edr-raw-(pipeline|replica)-.*", lcaas_id="{tenant}"}}'
        if sub == 'xql':
            query = f'stackdriver_pubsub_subscription_pubsub_googleapis_com_subscription_num_undelivered_messages{{subscription_id=~"lava-wall-.*", lcaas_id="{tenant}"}}'
        if sub == 'storybuilder':
            return
        if sub == 'dms':
            query = f'stackdriver_pubsub_subscription_pubsub_googleapis_com_subscription_num_undelivered_messages{{subscription_id=~"stitched-.*", lcaas_id="{tenant}"}}'
    return get_query_result(query)


"""
Now we have oldest message age , ack rate , number of unacked messages
We can calculate the time to process all messages before any changes 

increase of messages in the queue = number of unacked messages change per second 
so run get_number_of_unacked_messages time wait 1m then run again subtract the diff 

"""


def calculate_time_to_process_all_messages(sub, tenant):
    pass

def unacked_messages_change(sub, tenant):
    first_reading = get_number_of_unacked_messages(sub, tenant)
    time.sleep(20)
    second_reading = get_number_of_unacked_messages(sub, tenant)
    return (second_reading - first_reading)

def ack_rate_change(sub, tenant, iterations: int):
    readings = []
    for i in range(iterations):
        first_reading = query_subscription_ack_rate(sub, tenant)
        readings.append(first_reading)
        time.sleep(20)
        second_reading = query_subscription_ack_rate(sub, tenant)
        readings.append(second_reading)
        change = (second_reading - first_reading)
    return readings
    
"""This concludes the calculations for the subscription metrics
    up to here we can see if there is backlog building up and how long it will take to process all messages
    next we will look at the deployment metrics

    deployments :
    - pipeline
    - xql
    - dms
    - cronus
    - scylla
    - xcloud scylla
"""
if len(sys.argv) > 1:
    if sys.argv[1] == '-r' or sys.argv[1] == '--regional':
        env = 'reginoal'
    if sys.argv[1] == '-st' or sys.argv[1] == '--st':
        env = 'st'

def query_deployment_current_replica_and_max_hpa(deployment, tenant):
    if env == 'regional':
        if deployment == 'pipeline':
            query = f'count(up{{lcaas_id="{tenant}", app="xdr-st-$tenant-pipeline"}})'
        if deployment == 'xql':
            query = f'count(up{{lcaas_id="{tenant}", app=~"xdr-st-$tenant-xql-(.*)?.+"}})'
        if deployment == 'dms':
            query = f'count(up{{lcaas_id="{tenant}", app="xdr-st-$tenant-dms"}})'
        if deployment == 'cronus':
            query = f'count(up{{lcaas_id="{tenant}", kubernetes_pod_name=~".*cronus-node.*"}})'
        if deployment == 'scylla':
            query = f'count(up{{lcaas_id="{tenant}", kubernetes_pod_name=~".*scylla.*", kubernetes_namespace="scylla"}})'
        if deployment == 'xcloud-scylla':
            query = f'count(up{{lcaas_id="{tenant}", kubernetes_pod_name=~".*scylla.*", kubernetes_namespace="xcloud-scylla"}})'
    elif env == 'st':
        if deployment == 'redis':
            query = 'count(up{kubernetes_pod_name=~".*redis.*"})'
        if deployment == 'pipeline':
            query = 'count(up{app="xdr-st-$tenant-pipeline"})'
        if deployment == 'xql':
            query = 'count(up{app=~"xdr-st-$tenant-xql-(.*)?.+"})'
        if deployment == 'dms':
            query = 'count(up{app="xdr-st-$tenant-dms"})'
        if deployment == 'cronus':
            query = 'count(up{kubernetes_pod_name=~".*cronus-node.*"})'
        if deployment == 'scylla':
            query = 'count(up{kubernetes_pod_name=~".*scylla.*", kubernetes_namespace="scylla"})'
        if deployment == 'xcloud-scylla':
            query = 'count(up{kubernetes_pod_name=~".*scylla.*", kubernetes_namespace="xcloud-scylla"})'
    return get_query_result(query)

def query_deployment_cpu_usage(deployment, tenant):
    if env == 'st':
        if deployment == 'scylla':
            query = 'avg(kube:scylla_reactor_utilization:avg{kubernetes_namespace="scylla"})'
        if deployment == 'xcloud-scylla':
            query = 'avg(kube:scylla_reactor_utilization:avg{kubernetes_namespace="xcloud-scylla"})'
        if deployment == 'redis':
            query = 'kube:pod_cpu_usage:sum{namespace="xdr-st", pod=~".*redis.*"}'
        if deployment == 'pipeline':
            return
        if deployment == 'xql':
            return
        if deployment == 'dms':
            return
        if deployment == 'cronus':
            query = 'kube:pod_cpu_usage:sum{namespace="xdr-st", pod=~".*cronus-node.*"}'
    elif env == 'regional':
        if deployment == 'scylla':
            query = f'avg(kube:scylla_reactor_utilization:avg{{lcaas_id="{tenant}", kubernetes_namespace="scylla"}})'
        if deployment == 'xcloud-scylla':
            query = f'rate(container_cpu_usage_seconds_total{{lcaas_id="{tenant}", kubernetes_pod_name=~".*scylla.*", kubernetes_namespace="xcloud-scylla"}}[1m])'
    print(deployment)
    print("HERE***********\n**************")
    return get_query_result(query)

def query_database_delay(database, tenant):
    if env == 'st':
        if database == 'redis':
            query = 'redis_command_duration_seconds:histogram_quantile{quantile="0.99"}'
        if database == 'cronus':
            query = 'cronus_request_process_duration_seconds:p90'
        if database == 'scylla':
            query = 'max(scylla_storage_proxy_coordinator_read_latency_bucket)'
        if database == 'xcloud-scylla':
            query = ''
    elif env == 'regional':
        if database == 'redis':
            query = f'avg(kube:pod_cpu_usage:sum{{lcaas_id="{tenant}", namespace="xdr-st", pod=~".*redis.*"}})'
        if database == 'scylla':
            query = f'avg(kube:scylla_reactor_utilization:avg{{lcaas_id="{tenant}", kubernetes_namespace="scylla"}})'
        if database == 'xcloud-scylla':
            query = f'avg(kube:scylla_reactor_utilization:avg{{lcaas_id="{tenant}", kubernetes_namespace="xcloud-scylla"}})'
    return get_query_result(query)


def running(tenant):
    for sub in subscriptions:
        # subscription_instance = Subscription(sub, tenant)
        if sub == 'dms':
            print(f"\033[92m---------------\nChecking subscription stitched\n---------------\n\033[0m")
        else:
            print(f"\033[92m---------------\nChecking subscription {sub}\n---------------\n\033[0m")
        if trim_query_result(query_subscription_oldest_unacked_message_age(sub, tenant)) is not None:
            if sub != 'dms':
                oldest_unacked_message_age = query_subscription_oldest_unacked_message_age(sub, tenant)
                oldest_unacked_message_age = oldest_unacked_message_age['data']['result']
                if len(oldest_unacked_message_age) > 0:
                    print('-----------------\n-----------------')
                    p.pprint(oldest_unacked_message_age)
                    print(oldest_unacked_message_age[0]['value'][1])
                    oldest_unacked_message_age = oldest_unacked_message_age[0]['value'][1]
                print(f'Oldest unacked message age for sub {sub} is : {oldest_unacked_message_age}\n')
        if trim_query_result(query_subscription_ack_rate(sub, tenant)) is not None :
            print(f'Ack rate for sub {sub}: {query_subscription_ack_rate(sub, tenant)}\n')
        if trim_query_result(get_number_of_unacked_messages(sub, tenant)) is not None:
            unacked_messages = get_number_of_unacked_messages(sub, tenant)
            unacked_messages = unacked_messages['data']['result']
            if unacked_messages is not None:
                if len(unacked_messages) > 0:
                    unacked_messages = unacked_messages[0]['value'][1]
            print(f'number of unacked messages for sub {sub} is : {unacked_messages}\n')
        
    for dep in deployments:
        # if dep == 'scylla' or dep == 'xcloud-scylla' or dep == 'cronus':
        #     database_instance = Database(dep, tenant)
        print(f"Checking deployment {dep}")
        if query_deployment_current_replica_and_max_hpa(dep, tenant) is not None:
            replicas = query_deployment_current_replica_and_max_hpa(dep, tenant)
    
            replicas = replicas['data']['result']
            if len(replicas) > 0:
                print("-----------------\n-----------------")
                print(f'Current replicas: for deployment {dep} is :  {replicas[0]['value'][1]}\n')
            else:
                print(f'\033[93mNo replicas found for deployment {dep}\n\033[0m')
        if trim_query_result(query_deployment_cpu_usage(dep, tenant)) is not None:
            print(f'CPU usage for deployment {dep} is : {trim_query_result(query_deployment_cpu_usage(dep, tenant))}\n')

def trim_query_result(query_result):
    if query_result is None:
        return None
    if len(query_result['data']['result']) > 0:
        return query_result['data']['result'][0]['value'][1]
    else:
        return None

class Subscription():
    def __init__(self, name, tenant, oldest_unacked_message_age, ack_rate, number_of_unacked_messages):
        self.name = name
        self.tenant = tenant
        self.oldest_unacked_message_age = oldest_unacked_message_age
        self.ack_rate = ack_rate
        self.number_of_unacked_messages = number_of_unacked_messages
    def __str__(self):
        return f"Subscription: {self.name}\nOldest unacked message age: {self.oldest_unacked_message_age}\nAck rate: {self.ack_rate}\nNumber of unacked messages: {self.number_of_unacked_messages}"
def instantiate_subscription(sub, tenant):
    subscription_instance = Subscription(name=sub,
                                         tenant=tenant,
                                         oldest_unacked_message_age=trim_query_result(query_subscription_oldest_unacked_message_age(sub, tenant)),
                                         ack_rate=trim_query_result(query_subscription_ack_rate(sub, tenant)),
                                         number_of_unacked_messages=trim_query_result(get_number_of_unacked_messages(sub, tenant))
    )
    return subscription_instance

class Deployment():
    def __init__(self, name, tenant, current_replicas):
        self.name = name
        self.tenant = tenant
        self.current_replicas = current_replicas
    def __str__(self):
        return f"Deployment: {self.name}\nCurrent replicas: {self.current_replicas}"


def instantiate_deployment(dep, tenant):
    deployment_instance = Deployment(name=dep,
                                     tenant=tenant,
                                     current_replicas=trim_query_result(query_deployment_current_replica_and_max_hpa(dep, tenant))
                                     )
    return deployment_instance


class Database():
    def __init__(self, name, tenant, delay):
        self.name = name
        self.tenant = tenant
        self.delay = delay
    def __str__(self):
        return f"Database: {self.name}\nDelay: {self.delay}"


class Redis():
    def __init__(self, name, tenant, cpu_usage, memory_usage):
        self.name = name
        self.tenant = tenant
        self.cpu_usage = cpu_usage
        self.memory_usage = memory_usage

    def __str__(self):
        return f"Redis: {self.name}\nCPU usage: {self.cpu_usage}"
    


def instantiate_database(db, tenant):
    database_instance = Database(name=db,
                                 tenant=tenant,
                                 delay=trim_query_result(query_deployment_cpu_usage(deployment=db,
                                                                                    tenant=tenant)))
    return database_instance


databases_list = [
    'scylla',
    'xcloud-scylla',
    'cronus',
    'redis'
]


# subscriptions = [
#     'pipeline',
#     'xql',
#     'dms',
#     'storybuilder',
#     'dms'
# ]


def running_class(tenant):

    subscription_instances = {}
    for sub in subscriptions:
        subscription_instance = instantiate_subscription(sub, tenant)
        # print(f"Subscription {subscription_instance.name} has oldest unacked message age of {subscription_instance.oldest_unacked_message_age}")
        # print(f"Subscription {subscription_instance.name} has ack rate of {subscription_instance.ack_rate}")
        # print(f"Subscription {subscription_instance.name} has number of unacked messages of {subscription_instance.number_of_unacked_messages}")
        # print('-----------------\n-----------------')
        
        subscription_instances.update({subscription_instance.name: subscription_instance.ack_rate})
        print(subscription_instance)
    deployment_instances = []
    for dep in deployments:
        deployment_instance = instantiate_deployment(dep, tenant)
        deployment_instances.append(deployment_instance)
        # print(f"Deployment {deployment_instance.name} has current replicas of {deployment_instance.current_replicas}")
        # print('-----------------\n-----------------')
    database_instances = []
    for db in databases_list:
        database_instance = instantiate_database(db, tenant)
        database_instances.append(database_instance)
        # print(f"Database {database_instance.name} has delay of {database_instance.delay}")
        # print('-----------------\n-----------------')
    print('-----------------\n-----------------')
    print('Subscription Instances')
    p.pprint(subscription_instances)
    print('-----------------\n-----------------')
    print('-----------------\n-----------------')
    print('Deployment Instances')
    p.pprint([vars(x) for x in deployment_instances])
    print('-----------------\n-----------------')
    print('Database Instances')
    p.pprint([vars(x) for x in database_instances])
    return

def main():
    tenant = '9997464373539'
    # running(tenant)
    # running_class(tenant)

    working_map = running_class(tenant)

    

if __name__ == '__main__':
    main()