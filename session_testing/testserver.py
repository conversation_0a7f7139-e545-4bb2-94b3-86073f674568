import os
from flask import Flask, redirect, url_for, session, render_template_string
from authlib.integrations.flask_client import OAuth

# 1. App setup
app = Flask(__name__)
app.secret_key = os.urandom(24)

# 2. OAuth setup
oauth = OAuth(app)
oauth.register(
    name='google',
    client_id='YOUR_GOOGLE_CLIENT_ID',  # Replace with your Client ID
    client_secret='YOUR_GOOGLE_CLIENT_SECRET',  # Replace with your Client Secret
    access_token_url='https://accounts.google.com/o/oauth2/token',
    access_token_params=None,
    authorize_url='https://accounts.google.com/o/oauth2/auth',
    authorize_params=None,
    api_base_url='https://www.googleapis.com/oauth2/v1/',
    userinfo_endpoint='https://openidconnect.googleapis.com/v1/userinfo',  # OIDC endpoint
    client_kwargs={'scope': 'openid email profile'},
)

# --- Templates for simplicity ---
HOME_TPL = """
{% if user %}
  <h1>Welcome, {{ user.name }}!</h1>
  <p>Email: {{ user.email }}</p>
  <img src="{{ user.picture }}" alt="Profile Picture">
  <br><br>
  <a href="/logout">Logout</a>
{% else %}
  <h1>Welcome!</h1>
  <a href="/login">Login with Google</a>
{% endif %}
"""

# 3. Routes
@app.route('/')
def home():
    user = session.get('user')
    return render_template_string(HOME_TPL, user=user)

@app.route('/login')
def login():
    # Redirect the user to Google's authentication page
    redirect_uri = url_for('auth', _external=True)
    return oauth.google.authorize_redirect(redirect_uri)

@app.route('/auth')
def auth():
    # Handle the callback from Google
    token = oauth.google.authorize_access_token()
    # Fetch user info and store it in the session
    user_info = oauth.google.get('userinfo').json()
    session['user'] = user_info
    return redirect('/')

@app.route('/logout')
def logout():
    session.pop('user', None)
    return redirect('/')

if __name__ == '__main__':
    app.run(debug=True, port=5000)