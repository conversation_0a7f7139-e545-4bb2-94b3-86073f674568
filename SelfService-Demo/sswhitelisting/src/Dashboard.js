import React, { useState, useEffect, useCallback } from 'react';
// ... (other imports remain the same)
import BranchTenantSelection from './components/BranchTenantSelection';
import MetricInput from './components/MetricInput';
import MetricListDisplay from './components/MetricListDisplay';
import <PERSON><PERSON><PERSON><PERSON><PERSON> from './components/CardinalityChecker';
import LabelSelection from './components/LabelSelection';
import RecommendationEngine from './components/RecommendationEngine';
import JiraIntegration from './components/JiraIntegration';
import {
  MOCK_SOURCE_BRANCHES, MOCK_TARGET_BRANCHES, MOCK_QA_TENANTS, getAvailableLabelsForMetric
} from './utils/mockData';
import {
  runCardinalityAndEmissionCheck,
  generateRecordingRuleExample, // Generates initial rule { needed, example }
  generateAlertExamples,
  createJiraTicketApi
} from './utils/mockApi';


function Dashboard() {
  // ... (all existing state variables: sourceBranch, targetBranch, qaTenant, etc. remain the same)
  const [sourceBranch, setSourceBranch] = useState('');
  const [targetBranch, setTargetBranch] = useState('');
  const [qaTenant, setQaTenant] = useState('');
  const [qaTenantLcaasId, setQaTenantLcaasId] = useState('');

  useEffect(() => {
    if (!qaTenant) {
      setQaTenantLcaasId('');
    }
  }, [qaTenant]);

  const [metrics, setMetrics] = useState([]);
  const [metricIdCounter, setMetricIdCounter] = useState(0);

  const [cardinalityResults, setCardinalityResults] = useState({});
  const [isLoadingCardinality, setIsLoadingCardinality] = useState(false);

  const [selectedLabels, setSelectedLabels] = useState({});

  const [recordingRules, setRecordingRules] = useState({}); // Structure: { metricId: { needed: boolean, example: string (YAML or message) } }
  const [isLoadingRules, setIsLoadingRules] = useState(false);

  const [alertExamples, setAlertExamples] = useState({});
  const [isLoadingAlerts, setIsLoadingAlerts] = useState(false);

  const [jiraTicketInfo, setJiraTicketInfo] = useState(null);
  const [isCreatingJira, setIsCreatingJira] = useState(false);

  // ... (metric management functions: handleAddMetric, handleBatchAddMetrics, handleRemoveMetric remain the same)
  const handleAddMetric = useCallback((metricName, podName) => {
    if (!metricName.trim()) return;
    const newId = `metric-${metricIdCounter}`;
    setMetrics(prev => [...prev, { id: newId, name: metricName.trim(), pod: podName?.trim() || '' }]);
    setMetricIdCounter(prev => prev + 1);
  }, [metricIdCounter]);

  const handleBatchAddMetrics = useCallback((batchText) => {
    const lines = batchText.split('\n').filter(line => line.trim() !== '');
    let currentIdCounter = metricIdCounter;
    const newMetrics = lines.map(line => {
      const parts = line.split(',').map(p => p.trim());
      const id = `metric-${currentIdCounter++}`;
      return { id, name: parts[0], pod: parts[1] || '' };
    });
    setMetrics(prev => [...prev, ...newMetrics]);
    setMetricIdCounter(currentIdCounter);
  }, [metricIdCounter]);

  const handleRemoveMetric = useCallback((metricIdToRemove) => {
    setMetrics(prev => prev.filter(m => m.id !== metricIdToRemove));
    setCardinalityResults(prev => { const newState = {...prev}; delete newState[metricIdToRemove]; return newState; });
    setSelectedLabels(prev => { const newState = {...prev}; delete newState[metricIdToRemove]; return newState; });
    setRecordingRules(prev => { const newState = {...prev}; delete newState[metricIdToRemove]; return newState; });
    setAlertExamples(prev => { const newState = {...prev}; delete newState[metricIdToRemove]; return newState; });
  }, []);

  // ... (cardinality and labels functions: handleRunCardinalityChecks, handleLabelsChange remain the same)
  const handleRunCardinalityChecks = async () => {
    if (!qaTenant || metrics.length === 0 || !qaTenantLcaasId.trim()) {
      alert("To run cardinality checks, please select a QA tenant, provide its LCaaS ID, and add at least one metric.");
      return;
    }
    setIsLoadingCardinality(true);
    setCardinalityResults({});
    const results = await runCardinalityAndEmissionCheck(metrics, qaTenant, qaTenantLcaasId);
    setCardinalityResults(results);
    setIsLoadingCardinality(false);
  };

  const handleLabelsChange = (metricId, newLabels) => {
    setSelectedLabels(prev => ({ ...prev, [metricId]: newLabels }));
  };


  // --- Recommendations ---
  useEffect(() => { // Recording Rules (Initial Generation)
    const fetchRules = async () => {
       setIsLoadingRules(true);
      const newRules = {};
      for (const metric of metrics) {
        // Generate rules if labels are selected OR if cardinality has run for non-fatal metrics
        // This ensures we get the "not necessary" message if applicable.
        if ((selectedLabels[metric.id] || Object.keys(cardinalityResults).includes(metric.id))) {
          const ruleData = await generateRecordingRuleExample(metric.name, selectedLabels[metric.id] || []);
          newRules[metric.id] = ruleData; // ruleData should be { needed: boolean, example: string }
        }
      }
      setRecordingRules(newRules);
      setIsLoadingRules(false);
    };

    const shouldFetchRules = metrics.some(metric =>
        (selectedLabels[metric.id] || Object.keys(cardinalityResults).includes(metric.id))
    );

    if (metrics.length > 0 && (Object.keys(selectedLabels).length > 0 || Object.keys(cardinalityResults).length > 0) && shouldFetchRules) {
       fetchRules();
    } else if (metrics.length === 0) { // Clear rules if no metrics
        setRecordingRules({});
    }
  }, [selectedLabels, metrics, cardinalityResults]);


  useEffect(() => { // Alert Examples (Initial Generation)
    // ... (remains the same)
    const fetchAlerts = async () => {
      setIsLoadingAlerts(true);
      const newAlerts = {};
      for (const metric of metrics) {
        if (cardinalityResults[metric.id]?.status !== 'FatalX') {
          const alertData = await generateAlertExamples(metric.name, selectedLabels[metric.id], cardinalityResults[metric.id]);
          newAlerts[metric.id] = alertData;
        }
      }
      setAlertExamples(newAlerts);
      setIsLoadingAlerts(false);
    };
    if (metrics.length > 0 && Object.keys(cardinalityResults).length === metrics.length) {
      fetchAlerts();
    } else if (metrics.length === 0) { // Clear alerts if no metrics
        setAlertExamples({});
    }
  }, [metrics, cardinalityResults, selectedLabels]);

  // Handler for editable alert examples
  const handleAlertExampleChange = useCallback((metricId, newAlertYaml) => {
    // ... (remains the same)
    setAlertExamples(prevExamples => ({
      ...prevExamples,
      [metricId]: {
        ...(prevExamples[metricId] || {}),
        alerts: newAlertYaml,
      },
    }));
  }, []);

  // NEW: Handler for editable recording rule examples
  const handleRecordingRuleChange = useCallback((metricId, newRuleYaml) => {
    setRecordingRules(prevRules => ({
      ...prevRules,
      [metricId]: {
        ...(prevRules[metricId] || { needed: false }), // Preserve 'needed' or default if creating new
        example: newRuleYaml,
      },
    }));
  }, []);


  // --- Jira Integration ---
  const handleCreateJira = async () => {
    // ... (validation logic remains the same)
    if (!targetBranch || metrics.length === 0 || (qaTenant && !qaTenantLcaasId.trim()) ) {
        let alertMessage = "Please ensure Target Branch is selected and at least one metric is processed.";
        if (qaTenant && !qaTenantLcaasId.trim()) {
            alertMessage = "If an environment is selected, its LCaaS ID must also be provided for Jira creation.";
        }
        alert(alertMessage);
        return;
    }

    setIsCreatingJira(true);
    setJiraTicketInfo(null);

    const metricsSummaryTable = metrics.map(metric => {
        const cr = cardinalityResults[metric.id] || {};
        const labels = selectedLabels[metric.id] || [];
        const ruleInfo = recordingRules[metric.id];
        // Determine rule status for summary: "Yes (see example)", "No / Optional", or "Not Suggested"
        let ruleSummary = "Not Suggested";
        if (ruleInfo) {
            if (ruleInfo.needed && ruleInfo.example && !ruleInfo.example.includes("not be necessary")) {
                ruleSummary = "Yes (see example)";
            } else if (ruleInfo.example && !ruleInfo.example.includes("not be necessary")) {
                ruleSummary = "Defined (see example)"; // If not "needed" but has YAML
            } else {
                ruleSummary = "No / Optional"; // If "not necessary" message or no YAML
            }
        }

        return `| ${metric.name} | ${metric.pod || 'N/A'} | ${cr.status || 'N/A'} | ${cr.isOmitted ? 'Yes' : 'No'} | ${labels.join(', ') || 'N/A'} | ${ruleSummary} |`;
    }).join('\n');

    const jiraDescription = `
h2. Metrics Onboarding Request

h3. Environment Details
* Target Branch: ${targetBranch}
* Environment : ${qaTenant || 'N/A (Not Selected)'}
* QA Tenant LCaaS ID: ${qaTenant && qaTenantLcaasId ? qaTenantLcaasId : 'N/A'}

h3. Metrics Overview & Actions
|| Metric Name || Pod || Cardinality/Emission Status || Omitted || Selected Labels || Recording Rule Suggested/Defined ||
${metricsSummaryTable}

h3. Requested Rules and Alert review

${metrics.map(metric => {
    const cr = cardinalityResults[metric.id] || {};
    const ruleInfo = recordingRules[metric.id]; // This will now contain potentially edited rule YAML
    const alertInfo = alertExamples[metric.id];
    let details = `h4. Metric: ${metric.name}${metric.pod ? ` (Pod: ${metric.pod})` : ''}\n`;

    // Cardinality Info
    if (cr.message) {
        details += `* Cardinality/Emission on ${cr.checkedTenant || 'N/A'}${cr.checkedLcaasId ? ` (LCaaS ID: ${cr.checkedLcaasId})` : ''}: ${cr.status} - ${cr.message}\n`;
    } else if (qaTenant) {
        details += `* Cardinality/Emission: Pending for selected QA Tenant.\n`;
    }

    // Selected Labels
    if (selectedLabels[metric.id]?.length > 0) {
        details += `* Selected Labels: ${selectedLabels[metric.id].join(', ')}\n`;
    }

    // Recording Rule (now includes edited version)
    if (ruleInfo?.example && ruleInfo.example.trim() !== "" && !ruleInfo.example.includes("not be necessary")) {
        details += `* Recording Rule (Editable by Dev):\n{code:yaml}\n${ruleInfo.example.trim()}\n{code}\n`;
    } else if (ruleInfo?.example && ruleInfo.example.includes("not be necessary")) {
        details += `* Recording Rule: ${ruleInfo.example}\n`; // Shows "Not necessary..." message
    } else if (ruleInfo?.needed) {
        details += `* Recording Rule: (Marked as needed, but no YAML provided/edited yet)\n`;
    }


    // Alert Examples (already handles edited versions)
    if (alertInfo?.alerts && alertInfo.alerts.trim() !== "") {
        details += `* Alert Examples (Editable by Dev):\n`;
        if (alertInfo.message && alertInfo.message.trim() !== "" && !alertInfo.alerts.includes(alertInfo.message.trim())) {
            details += `{noformat}\n${alertInfo.message.trim()}\n{noformat}\n`;
        }
        details += `{code:yaml}\n${alertInfo.alerts.trim()}\n{code}\n`;
    } else if (alertInfo?.message) {
        details += `* Alert Examples: ${alertInfo.message}\n`;
    }
    return details;
}).join('\n')}

h3. SRE Action Items
* Review proposed metrics, labels, and cardinality findings.
* Review, approve, and implement proposed/edited recording rules.
* Review and configure proposed/edited alerts.
* Merge changes upon successful validation.
    `;

    const jiraPayload = {
      summary: `Metrics Onboarding Request for ${metrics.map(m => m.name).join(', ')}${qaTenant ? ` (QA: ${qaTenant})` : ''}`,
      description: jiraDescription,
    };

    const response = await createJiraTicketApi(jiraPayload);
    if (response.success) {
      setJiraTicketInfo(response.ticketInfo);
    } else {
      alert(`Jira creation failed (simulated): ${response.message}`);
    }
    setIsCreatingJira(false);
  };

  // ... (actionableMetrics, isJiraDisabled, and return JSX remain the same)
  // but ensure onRecordingRuleChange is passed to RecommendationEngine
  const actionableMetrics = metrics.filter(m => {
    if (!qaTenant && !cardinalityResults[m.id]) return true;
    return cardinalityResults[m.id]?.status ;
  });

  const isJiraDisabled = metrics.length === 0 || !sourceBranch || !targetBranch || (qaTenant && !qaTenantLcaasId.trim());


  return (
    <div>
      <BranchTenantSelection
        sourceBranch={sourceBranch} setSourceBranch={setSourceBranch} sourceBranches={MOCK_SOURCE_BRANCHES}
        targetBranch={targetBranch} setTargetBranch={setTargetBranch} targetBranches={MOCK_TARGET_BRANCHES}
        qaTenant={qaTenant} setQaTenant={setQaTenant} qaTenants={MOCK_QA_TENANTS}
        qaTenantLcaasId={qaTenantLcaasId} setQaTenantLcaasId={setQaTenantLcaasId}
      />
      <hr />
      <MetricInput onAddMetric={handleAddMetric} onBatchAddMetrics={handleBatchAddMetrics} />
      <MetricListDisplay metrics={metrics} onRemoveMetric={handleRemoveMetric} />
      <hr />
      <CardinalityChecker
        onRunChecks={handleRunCardinalityChecks}
        results={cardinalityResults}
        metrics={metrics}
        isLoading={isLoadingCardinality}
        qaTenant={qaTenant}
        qaTenantLcaasId={qaTenantLcaasId}
      />
      <hr />
      {metrics.length > 0 && (
        <LabelSelection
          metrics={actionableMetrics}
          selectedLabels={selectedLabels}
          onLabelsChange={handleLabelsChange}
          getAvailableLabelsForMetric={getAvailableLabelsForMetric}
          cardinalityResults={cardinalityResults}
        />
      )}
      <hr />
       <RecommendationEngine
        metrics={metrics}
        recordingRules={recordingRules}
        onRecordingRuleChange={handleRecordingRuleChange} // <<< Pass new handler
        alertExamples={alertExamples}
        onAlertExampleChange={handleAlertExampleChange}
        isLoadingRules={isLoadingRules}
        isLoadingAlerts={isLoadingAlerts}
        cardinalityResults={cardinalityResults}
        selectedLabels={selectedLabels}
      />
      <hr />
      <JiraIntegration
        onCreateJira={handleCreateJira}
        isCreating={isCreatingJira}
        ticketInfo={jiraTicketInfo}
        disabled={isJiraDisabled}
        metrics={metrics}
      />
    </div>
  );
}
export default Dashboard;