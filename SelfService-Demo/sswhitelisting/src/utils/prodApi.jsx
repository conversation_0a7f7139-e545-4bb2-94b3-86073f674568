import React, { useState } from 'react';
import axios from 'axios';

function cardinality_checker({ initialMetrics, initialQaTenant, initialQaTenantLcaasId }) {
  // State to hold the data that will be sent
  // You might get these values from user input, props, or other parts of your app
  const [metrics, setMetrics] = useState(initialMetrics || '');
  const [qaTenant, setQaTenant] = useState(initialQaTenant || '');
  const [qaTenantLcaasId, setQaTenantLcaasId] = useState(initialQaTenantLcaasId || '');
  const [apiResponse, setApiResponse] = useState(null);
  const [error, setError] = useState(null);
  const [isLoading, setIsLoading] = useState(false);

  // Function to handle the API call
  const sendDataToFlask = async () => {
    // 1. Construct the JSON payload
    const payload = {
      metrics: metrics,
      qaTenant: qaTenant,
      qaTenantLcaasId: qaTenantLcaasId,
    };

    // Log the payload to see what you're sending (for debugging)
    console.log('Sending JSON Payload:', payload);

    setIsLoading(true);
    setApiResponse(null); // Clear previous response
    setError(null);       // Clear previous error

    try {
      // 2. Make the API call using Axios
      // Replace 'http://127.0.0.1:5000/process_data' with actual Flask API endpoint
      const response = await axios.post('http://127.0.0.1:5000/process_data', payload, {
        headers: {
          'Content-Type': 'application/json' // Axios often sets this automatically for objects, but good to be explicit
        }
      });

      // 3. Handle the successful response
      console.log('API Response:', response.data);
      setApiResponse(response.data);
    } catch (err) {
      // 4. Handle errors
      if (err.response) {
        // The request was made and the server responded with a status code
        // that falls out of the range of 2xx
        console.error('API Error Response:', err.response.data);
        setError(`Server Error: ${err.response.data.message || 'Unknown server error'}`);
      } else if (err.request) {
        // The request was made but no response was received
        console.error('Network Error: No response received', err.request);
        setError('Network Error: No response from server. Is the Flask API running?');
      } else {
        // Something happened in setting up the request that triggered an Error
        console.error('Axios Error:', err.message);
        setError(`Client Error: ${err.message}`);
      }
    } finally {
      setIsLoading(false); // End loading regardless of success or failure
    }
  };

  return (
    <div style={{ padding: '20px', border: '1px solid #ccc', borderRadius: '8px', maxWidth: '500px', margin: '20px auto' }}>
      <h2>Send Data to Flask API</h2>

      <div style={{ marginBottom: '10px' }}>
        <label htmlFor="metricsInput" style={{ display: 'block', marginBottom: '5px' }}>Metrics:</label>
        <input
          id="metricsInput"
          type="text"
          value={metrics}
          onChange={(e) => setMetrics(e.target.value)}
          placeholder="e.g., usage, performance"
          style={{ width: '100%', padding: '8px', boxSizing: 'border-box' }}
        />
      </div>

      <div style={{ marginBottom: '10px' }}>
        <label htmlFor="qaTenantInput" style={{ display: 'block', marginBottom: '5px' }}>QA Tenant:</label>
        <input
          id="qaTenantInput"
          type="text"
          value={qaTenant}
          onChange={(e) => setQaTenant(e.target.value)}
          placeholder="e.g., tenant_a"
          style={{ width: '100%', padding: '8px', boxSizing: 'border-box' }}
        />
      </div>

      <div style={{ marginBottom: '20px' }}>
        <label htmlFor="qaTenantLcaasIdInput" style={{ display: 'block', marginBottom: '5px' }}>QA Tenant LCAAS ID:</label>
        <input
          id="qaTenantLcaasIdInput"
          type="text"
          value={qaTenantLcaasId}
          onChange={(e) => setQaTenantLcaasId(e.target.value)}
          placeholder="e.g., 12345"
          style={{ width: '100%', padding: '8px', boxSizing: 'border-box' }}
        />
      </div>

      <button
        onClick={sendDataToFlask}
        disabled={isLoading || !metrics || !qaTenant || !qaTenantLcaasId} // Disable if loading or inputs are empty
        style={{ padding: '10px 20px', backgroundColor: '#007bff', color: 'white', border: 'none', borderRadius: '5px', cursor: 'pointer' }}
      >
        {isLoading ? 'Sending...' : 'Send Data to Flask'}
      </button>

      {isLoading && <p style={{ color: '#007bff' }}>Loading...</p>}
      {error && <p style={{ color: 'red' }}>Error: {error}</p>}
      {apiResponse && (
        <div style={{ marginTop: '20px', backgroundColor: '#e9f7ef', padding: '15px', borderRadius: '5px', border: '1px solid #cce8d0' }}>
          <h3>API Response:</h3>
          <pre style={{ whiteSpace: 'pre-wrap', wordBreak: 'break-all' }}>
            {JSON.stringify(apiResponse, null, 2)}
          </pre>
        </div>
      )}
    </div>
  );
}

export default cardinality_checker;



//async (metrics, qaTenant, qaTenantLcaasId) =>


// flask endpoint : 
// from flask import Flask, request, jsonify
// from flask_cors import CORS # Import CORS

// app = Flask(__name__)
// CORS(app) # Enable CORS for all routes (important for React frontend)
//
// # Jira API Configuration
// JIRA_URL = os.getenv("JIRA_URL", "https://your-domain.atlassian.net") # Replace with your Jira instance URL
// JIRA_EMAIL = os.getenv("JIRA_EMAIL", "<EMAIL>") # Replace with your Jira email
// JIRA_API_TOKEN = os.getenv("JIRA_API_TOKEN", "YOUR_JIRA_API_TOKEN") # Replace with your Jira API token

// # Replace with your actual project key and issue type name
// JIRA_PROJECT_KEY = os.getenv("JIRA_PROJECT_KEY", "PROJ")
// JIRA_ISSUE_TYPE_NAME = os.getenv("JIRA_ISSUE_TYPE_NAME", "Task")

// # Custom field IDs (replace with your actual custom field IDs from Jira)
// # If you don't have custom fields, you can map these to the issue description or summary.
// CUSTOM_FIELD_METRICS = os.getenv("CUSTOM_FIELD_METRICS", "customfield_10001") # Example custom field for metrics
// CUSTOM_FIELD_CARDINALITY = os.getenv("CUSTOM_FIELD_CARDINALITY", "customfield_10002") # Example custom field for cardinality
// CUSTOM_FIELD_RULES = os.getenv("CUSTOM_FIELD_RULES", "customfield_10003") # Example custom field for rules
// CUSTOM_FIELD_ALERTS = os.getenv("CUSTOM_FIELD_ALERTS", "customfield_10004") # Example custom field for alerts




// @app.route('/process_data', methods=['POST'])
// def process_data():
//     if request.is_json:
//         data = request.get_json()

//         # Extract the fields
//         metrics = data.get('metrics')
//         env_prefix = data.get('qaTenant')
//         lcaas_id = data.get('qaTenantLcaasId')

//         # ---  Flask Logic Here ---
//         

//         # Trigger Cardinality script  
//         print(f"Received metrics: {metrics}")
//         print(f"Received QA Tenant: {env_prefix}")
//         print(f"Received QA Tenant LCAAS ID: {lcaas_id}")

//         # Example: Return a success message and the received data
//         return jsonify({
//             "status": "success",
//             "message": "Data received and processed successfully!",
//             "received_data": {
//                 "metrics": metrics,
//                 "qaTenant": qa_tenant,
//                 "qaTenantLcaasId": qa_tenant_lcaas_id
//             }
//         }), 200
//     else:
//         return jsonify({"status": "error", "message": "Request must be JSON"}), 400

// @app.route('/create_jira_issue', methods=['POST'])
// def create_jira_issue():
//     if not request.is_json:
//         return jsonify({"error": "Request must be JSON"}), 400

//     data = request.get_json()

//     # Extract data from the request body
//     metrics_per_pod = data.get('metricsPerPod')
//     cardinality_per_pod = data.get('cardinalityPerPod')
//     rules = data.get('rules')
//     alerts = data.get('alerts')
//     summary = data.get('summary', 'New Issue from Monitoring Alert') # Default summary
//     description_prefix = "Issue created from a monitoring alert with the following details:\n\n"

//     # Construct the Jira issue payload
//     issue_fields = {
//         "project": {
//             "key": JIRA_PROJECT_KEY
//         },
//         "summary": summary,
//         "issuetype": {
//             "name": JIRA_ISSUE_TYPE_NAME
//         },
//         "description": {
//             "type": "doc",
//             "version": 1,
//             "content": [
//                 {
//                     "type": "paragraph",
//                     "content": [
//                         {
//                             "type": "text",
//                             "text": description_prefix
//                         }
//                     ]
//                 }
//             ]
//         }
//     }

//     # Add variables to the description or custom fields
//     if metrics_per_pod:
//         issue_fields["description"]["content"].append({
//             "type": "paragraph",
//             "content": [
//                 {"type": "text", "text": f"Metrics and Cardinality per Pod:\n{metrics_per_pod}"}
//             ]
//         })
//         # If you have a custom field for metrics, you can add it here:
//         # issue_fields[CUSTOM_FIELD_METRICS] = metrics_per_pod
    
//     if cardinality_per_pod:
//         # If you have a custom field for cardinality, you can add it here:
//         # issue_fields[CUSTOM_FIELD_CARDINALITY] = cardinality_per_pod
//         # Or just append to description if not using a separate custom field
//         if not metrics_per_pod: # only append if not already appended with metrics_per_pod
//             issue_fields["description"]["content"].append({
//                 "type": "paragraph",
//                 "content": [
//                     {"type": "text", "text": f"Cardinality per Pod:\n{cardinality_per_pod}"}
//                 ]
//             })

//     if rules:
//         issue_fields["description"]["content"].append({
//             "type": "paragraph",
//             "content": [
//                 {"type": "text", "text": f"Rules Triggered:\n{rules}"}
//             ]
//         })
//         # issue_fields[CUSTOM_FIELD_RULES] = rules

//     if alerts:
//         issue_fields["description"]["content"].append({
//             "type": "paragraph",
//             "content": [
//                 {"type": "text", "text": f"Alerts Details:\n{alerts}"}
//             ]
//         })
//         # issue_fields[CUSTOM_FIELD_ALERTS] = alerts

//     jira_payload = {
//         "fields": issue_fields
//     }

//     headers = {
//         "Accept": "application/json",
//         "Content-Type": "application/json"
//     }

//     auth = HTTPBasicAuth(JIRA_EMAIL, JIRA_API_TOKEN)
//     jira_api_endpoint = f"{JIRA_URL}/rest/api/3/issue"

//     try:
//         response = requests.post(
//             jira_api_endpoint,
//             headers=headers,
//             json=jira_payload,
//             auth=auth
//         )
//         response.raise_for_status() # Raise an HTTPError for bad responses (4xx or 5xx)

//         jira_response = response.json()
//         return jsonify({
//             "message": "Jira issue created successfully!",
//             "issue_id": jira_response.get("id"),
//             "issue_key": jira_response.get("key"),
//             "issue_url": f"{JIRA_URL}/browse/{jira_response.get('key')}"
//         }), 200

//     except requests.exceptions.HTTPError as e:
//         print(f"HTTP Error creating Jira issue: {e.response.text}")
//         return jsonify({"error": "Failed to create Jira issue", "details": e.response.json()}), e.response.status_code
//     except requests.exceptions.RequestException as e:
//         print(f"Request Error creating Jira issue: {e}")
//         return jsonify({"error": "An error occurred while connecting to Jira", "details": str(e)}), 500
//     except Exception as e:
//         print(f"Unexpected Error: {e}")
//         return jsonify({"error": "An unexpected error occurred", "details": str(e)}), 500

// if __name__ == '__main__':
//     # For development, enable CORS
//     from flask_cors import CORS
//     CORS(app)
//     app.run(debug=True, port=5000)



function jira_creator({ initialMetrics, initialQaTenant, initialQaTenantLcaasId, initialRules, initialAlerts , initialCardinality}) {
  // State to hold the data that will be sent
  // You might get these values from user input, props, or other parts of your app
  const [metrics, setMetrics] = useState(initialMetrics || '');
  const [qaTenant, setQaTenant] = useState(initialQaTenant || '');
  const [qaTenantLcaasId, setQaTenantLcaasId] = useState(initialQaTenantLcaasId || '');
  const [rules, setRules] = useState(initialRules || '');
  const [alerts, setAlerts] = useState(initialAlerts || '');
  const [cardinality, setCardinality] = useState(initialCardinality || '');
  const [apiResponse, setApiResponse] = useState(null);
  const [error, setError] = useState(null);
  const [isLoading, setIsLoading] = useState(false);

  // Function to handle the API call
  const sendDataToFlask = async () => {
    // 1. Construct the JSON payload
    const payload = {
      metrics: metrics,
      Cardinality: cardinality,
      Tenant: qaTenantLcaasId+''+qaTenant,
      rules: rules,
      alerts: alerts
    };

    // Log the payload to see what you're sending (for debugging)
    console.log('Sending JSON Payload:', payload);

    setIsLoading(true);
    setApiResponse(null); // Clear previous response
    setError(null);       // Clear previous error

    try {
      // 2. Make the API call using Axios
      // Replace 'http://127.0.0.1:5000/process_data' with your actual Flask API endpoint
      const response = await axios.post('http://127.0.0.1:5000/process_data', payload, {
        headers: {
          'Content-Type': 'application/json' // Axios often sets this automatically for objects, but good to be explicit
        }
      });

      // 3. Handle the successful response
      console.log('API Response:', response.data);
      setApiResponse(response.data);
    } catch (err) {
      // 4. Handle errors
      if (err.response) {
        // The request was made and the server responded with a status code
        // that falls out of the range of 2xx
        console.error('API Error Response:', err.response.data);
        setError(`Server Error: ${err.response.data.message || 'Unknown server error'}`);
      } else if (err.request) {
        // The request was made but no response was received
        console.error('Network Error: No response received', err.request);
        setError('Network Error: No response from server. Is the Flask API running?');
      } else {
        // Something happened in setting up the request that triggered an Error
        console.error('Axios Error:', err.message);
        setError(`Client Error: ${err.message}`);
      }
    } finally {
      setIsLoading(false); // End loading regardless of success or failure
    }
  };

