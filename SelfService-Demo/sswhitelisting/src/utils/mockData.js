// This is where we setup default labels and or scrape labels from results bucket json api call  

export const MOCK_SOURCE_BRANCHES = ['master-current', 'dev-upcoming'];
export const MOCK_TARGET_BRANCHES = ['master (current-version)', 'dev (upcoming version)'];
export const MOCK_QA_TENANTS = ['qa2-test', 'prod-us', 'prod-kr', 'prod-uk', 'prod-de', 'prod-au' ];

export const MOCK_AVAILABLE_LABELS_PER_METRIC_TYPE = {
  // Generic labels, you might have specific ones per actual metric name pattern
  "http_requests": ["code", "handler", "instance", "job", "method", "path", "status_code"],
  "cpu_usage": ["cpu", "instance", "job", "mode", "namespace", "pod", "container"],
  "memory_usage": ["instance", "job", "namespace", "pod", "container"],
  "custom_app_metric": ["app_version", "customer_id", "region", "instance"],
  "default": ["product_tier", "product_type", "tenant_type", "kubernetes_namespace", "xdr_id","namespace", "lcaas_id" ] // Default set if metric name doesn't match
};
//Required (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id)
// Function to get labels based on a metric name (simple match)
export const getAvailableLabelsForMetric = (metricName) => {
  if (metricName.includes("http_request")) return MOCK_AVAILABLE_LABELS_PER_METRIC_TYPE["http_requests"];
  if (metricName.includes("cpu_usage")) return MOCK_AVAILABLE_LABELS_PER_METRIC_TYPE["cpu_usage"];
  if (metricName.includes("memory_usage")) return MOCK_AVAILABLE_LABELS_PER_METRIC_TYPE["memory_usage"];
  if (metricName.includes("custom_app")) return MOCK_AVAILABLE_LABELS_PER_METRIC_TYPE["custom_app_metric"];
  return MOCK_AVAILABLE_LABELS_PER_METRIC_TYPE["default"];
};