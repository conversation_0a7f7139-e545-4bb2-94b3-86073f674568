// Simulates an API call with a delay
const simulateApiCall = (data, delay = 700) =>
  new Promise((resolve) => setTimeout(() => resolve(data), delay));

// Simulates running cardinality check and emission status
export const runCardinalityAndEmissionCheck = async (metrics, qaTenant, qaTenantLcaasId) => {
  console.log(`Simulating cardinality check for ${metrics.length} metrics on ${qaTenant} (LCaaS ID: ${qaTenantLcaasId})...`);
  const results = {};
  metrics.forEach(metric => {
    // this block needs to be replaced 
    const randomFactor = Math.random();
    let status = 'OK';
    let message = `Metric ${metric.name} is present and cardinality is within limits on ${qaTenant} (LCaaS ID: ${qaTenantLcaasId}).`;
    let isOmitted = false;
    // Here is where we insert our cardinality conditions after getting our result from the api 
    //if metric['cardinality'][1] > 1000 "per pod" == extreme Fatal label 
    //if metric['cardinality'][1] between 30 and 1000 == warning 
    //if metric['cardinality'][1] < 30 green light - 
    //else metric is not emitted 
    if (randomFactor < 0.15) {
      status = 'Fatal';
      message = `CRITICAL: Metric ${metric.name} is omitted on ${qaTenant+'-'}${qaTenantLcaasId}) or has EXTREME cardinality. Action required.`;
      isOmitted = true;
    } else if (randomFactor < 0.4) {
      status = 'Warning';
      message = `WARNING: Metric ${metric.name} has HIGH CARDINALITY on ${qaTenant} (LCaaS ID: ${qaTenantLcaasId}). Review labels.`;
      isOmitted = false;
    }
    //metric['cardinality'][1] == cardinality 
    //results[metric.id] = { cardinality, status, message, isOmitted, checkedTenant: qaTenant, checkedLcaasId: qaTenantLcaasId };
    results[metric.id] = { status, message, isOmitted, checkedTenant: qaTenant, checkedLcaasId: qaTenantLcaasId };
  });
  return simulateApiCall(results); 
};

// Simulates generating a recording rule example
export const generateRecordingRuleExample = async (metricName, selectedLabels) => {
  if (!selectedLabels || selectedLabels.length === 0) {
    return simulateApiCall({ // Make sure to use simulateApiCall here too
      needed: false,
      example: "/* No labels selected, or recording rule may not be necessary . */"
    });
  }

  const baseMetricName = metricName.replace(/[^a-zA-Z0-9_]/g, '_');
  const newMetricName = `${baseMetricName}_by_${selectedLabels.join('_')}`;
  const labelsString = selectedLabels.join(', ');

  const example = `
# Recording Rule Example for ${metricName}
# SRE TEAM REVIEW AND APPROVAL REQUIRED FOR ALL RECORDING RULES.
# This is an auto-generated suggestion. PLEASE add labels and remove unneeded rules accordingly 

  - record: tenant:${metricName}:sum
    expr: sum by (kubernetes_namespace, tenant_type, product_type, product_tier, xdr_id, namespace, lcaas_id) (${metricName}) # Or avg, max, etc. depending on use case

`;
  return simulateApiCall({ needed: true, example }); // And here
};

// Simulates generating alert examples
export const generateAlertExamples = async (metricName, selectedLabels, cardinalityStatus) => {
  const baseMetricNameForAlert = metricName.replace(/[^a-zA-Z0-9_]/g, '_');
  let targetMetric = metricName;
  if (cardinalityStatus && cardinalityStatus.status !== 'Fatal' && selectedLabels && selectedLabels.length > 0) {
      targetMetric = `${baseMetricNameForAlert}_by_${selectedLabels.join('_')}`;
  }

  const examples = [
    {
      name: `Xpanse-High-Rate-${baseMetricNameForAlert.charAt(0).toUpperCase() + baseMetricNameForAlert.slice(1)}-Last-5m`,
      expr: `sum by (tenant_id)((rate(${targetMetric}{job="your-job"}[5m]))) > 100`,
      forDuration: '5m',
      severity: 'warning',
      summary: `High rate reporting for ${metricName} on {{ $labels.instance }}.`,
      description: `The metric ${targetMetric} on instance {{ $labels.instance }} is reporting a rate of {{ $value }}, exceeding the threshold.`,
      runbook:'confluence link',
    },
    {
      name: `XDR-Missing-Metric-${baseMetricNameForAlert.charAt(0).toUpperCase() + baseMetricNameForAlert.slice(1)}Absent`,
      expr: `absent(${targetMetric}{job="your-job"})`,
      forDuration: '10m',
      severity: 'critical',
      summary: `Metric ${metricName} is absent for job 'your-job'.`,
      description: `The metric ${targetMetric} (or its aggregated form) has not been seen for the last 10 minutes for job 'your-job'.`,
      runbook:'confluence link',
    }
  ];

  if (cardinalityStatus && cardinalityStatus.status === 'Warning') {
      examples.unshift({
          name: `XDR-High-Error-Rate-${baseMetricNameForAlert.charAt(0).toUpperCase() + baseMetricNameForAlert.slice(1)}`,
          expr: `sum by (tenant_id) (${metricName})) > 5000`,
          forDuration: '1h',
          severity: 'info',
          summary: `High cardinality detected for ${metricName}.`,
          description: `The metric ${metricName} has a high number of series. Investigate label selection or consider aggregation. Current series count: {{ $value }}.`,
          runbook:'confluence link',
      });
  }

  const alertYaml = examples.map(alert => `
  - alert: ${alert.name}
    expr: ${alert.expr}
    for: ${alert.forDuration}
    labels:
      pd_service: < Specify > 
      severity: ${alert.severity}
    annotations:
      summary: ${alert.summary}
      description: ${alert.description}
`).join('');

  return simulateApiCall({
    message: "Below are some common alert patterns. Adjust expressions, thresholds, and labels as per your specific requirements. SRE review is recommended.",
    alerts: alertYaml
  });
};

// Simulates creating a Jira ticket
export const createJiraTicketApi = async (jiraPayload) => {
  console.log("Simulating Jira Ticket Creation with payload:", jiraPayload);
  const ticketId = `JIRA-${Math.floor(Math.random() * 9000) + 1000}`;
  const ticketUrl = `https://your-jira-instance.com/browse/${ticketId}`;
  return simulateApiCall({ //  here need to insert actual api call 
    success: true,
    message: `Jira ticket ${ticketId} simulated successfully!`,
    ticketInfo: { id: ticketId, url: ticketUrl, summary: jiraPayload.summary },
  }, 1000);
};