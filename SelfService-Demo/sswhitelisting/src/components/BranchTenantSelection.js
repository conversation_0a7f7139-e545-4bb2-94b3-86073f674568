import React from 'react';

function BranchTenantSelection({
  sourceBranch, setSourceBranch, sourceBranches,
  targetBranch, setTargetBranch, targetBranches,
  qaTenant, setQaTenant, qaTenants,
  qaTenantLcaasId, setQaTenantLcaasId // <-- New props
}) {
  return (
    <section className="dashboard-section">
      <h2>1. Select Branch, Environment, and enter LcaaS ID</h2>
      <div className="form-group">
        <label htmlFor="targetBranch">Target Branch:</label>
        <select id="targetBranch" value={targetBranch} onChange={(e) => setTargetBranch(e.target.value)}>
          <option value="">-- Select Target Branch --</option>
          {targetBranches.map(branch => <option key={branch} value={branch}>{branch}</option>)}
        </select>
      </div>
      <div className="form-group">
        <label htmlFor="qaTenant">Environment</label>
        <select id="qaTenant" value={qaTenant} onChange={(e) => setQaTenant(e.target.value)}>
          <option value="">-- Select Environment --</option>
          {qaTenants.map(tenant => <option key={tenant} value={tenant}>{tenant}</option>)}
        </select>
      </div>
      {/* New Input Field for LCaaS ID */}
      <div className="form-group">
        <label htmlFor="qaTenantLcaasId">LCaaS ID:</label>
        <input
          type="text"
          id="qaTenantLcaasId"
          value={qaTenantLcaasId}
          onChange={(e) => setQaTenantLcaasId(e.target.value)}
          placeholder="Enter LCaaS ID for the selected Environment"
        />
      </div>
    </section>
  );
}

export default BranchTenantSelection;