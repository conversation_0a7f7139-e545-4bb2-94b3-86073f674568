import React from 'react';

function LabelSelection({ metrics, selectedLabels, onLabelsChange, getAvailableLabelsForMetric, cardinalityResults }) {

  const handleCheckboxChange = (metricId, label, isChecked) => {
    const currentLabels = selectedLabels[metricId] || [];
    let newLabels;
    if (isChecked) {
      newLabels = [...currentLabels, label];
    } else {
      newLabels = currentLabels.filter(l => l !== label);
    }
    onLabelsChange(metricId, newLabels);
  };

  const actionableMetrics = metrics.filter(m => cardinalityResults[m.id]?.status !== 'FatalX');

  if (actionableMetrics.length === 0 && metrics.length > 0) {
      return (
        <section className="dashboard-section">
            <h2>4. Select Required Labels</h2>
            <p className="message message-info">No metrics available for label selection (either all have Fatal issues or none processed yet).</p>
        </section>
      )
  }
   if (metrics.length === 0) {
    return null; // Or some placeholder if no metrics are added yet
  }


  return (
    <section className="dashboard-section">
      <h2>4. Select Required Labels (for non-Fatal metrics)</h2>
      {actionableMetrics.map(metric => {
        const available = getAvailableLabelsForMetric(metric.name);
        const currentMetricLabels = selectedLabels[metric.id] || [];
        const metricCardinalityStatus = cardinalityResults[metric.id]?.status;

        return (
          <div key={metric.id} className="label-selection-container">
            <h4>
              Metric: {metric.name}
              {metric.pod ? ` (Pod: ${metric.pod})` : ''}
              {metricCardinalityStatus && ` - Status: ${metricCardinalityStatus}`}
            </h4>
            {metricCardinalityStatus === 'Warning' && (
                <p className="message message-warning" style={{fontSize: '0.9em', padding: '8px'}}>
                    This metric has a cardinality warning. Please select labels judiciously to reduce series count.
                </p>
            )}
            <div className="label-group">
              <strong>Available Labels:</strong>
              {available.length > 0 ? available.map(label => (
                <label key={label}>
                  <input
                    type="checkbox"
                    value={label}
                    checked={currentMetricLabels.includes(label)}
                    onChange={(e) => handleCheckboxChange(metric.id, label, e.target.checked)}
                  /> {label}
                </label>
              )) : <p>No specific labels predefined for this metric type. Consider common labels like 'instance', 'job'.</p>}
            </div>
          </div>
        );
      })}
    </section>
  );
}

export default LabelSelection;