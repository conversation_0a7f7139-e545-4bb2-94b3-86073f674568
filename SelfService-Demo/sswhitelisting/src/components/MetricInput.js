import React, { useState } from 'react';

function MetricInput({ onAddMetric, onBatchAddMetrics }) {
  const [metricName, setMetricName] = useState('');
  const [podName, setPodName] = useState('');
  const [batchInput, setBatchInput] = useState('');

  const handleAdd = () => {
    if (metricName.trim()) {
      onAddMetric(metricName, podName);
      setMetricName('');
      setPodName('');
    }
  };

  const handleBatch = () => {
    if (batchInput.trim()) {
      onBatchAddMetrics(batchInput);
      setBatchInput('');
    }
  };

  return (
    <section className="dashboard-section">
      <h2>2. Add Metrics</h2>
      <div className="form-group">
        <label htmlFor="metricName">Metric Name:</label>
        <input
          type="text"
          id="metricName"
          value={metricName}
          onChange={(e) => setMetricName(e.target.value)}
          placeholder="e.g., http_requests_total"
        />
      </div>
      <button onClick={handleAdd} disabled={!metricName.trim()}>Add Metric Individually</button>

      <div className="form-group" style={{ marginTop: '20px' }}>
        <label htmlFor="batchInput">Batch Add Metrics (CSV format: metric_name - one per line):</label>
        <textarea
          id="batchInput"
          value={batchInput}
          onChange={(e) => setBatchInput(e.target.value)}
          placeholder="metric_one,pod-a&#10;metric_two&#10;metric_three,pod-c"
        />
      </div>
      <button onClick={handleBatch} disabled={!batchInput.trim()}>Add Batch</button>
    </section>
  );
}

export default MetricInput;