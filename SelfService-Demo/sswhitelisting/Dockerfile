# Stage 1: Build the React application
# This stage remains the same as before
FROM node:20-alpine AS build-stage

WORKDIR /app

# Copy package.json and package-lock.json (or yarn.lock)
COPY package*.json ./
# Install dependencies
RUN npm install
# Copy the rest of the application source code
COPY . .

# Build the application for production

RUN npm run build


# Stage 2: Serve the application using Apache HTTP Server
FROM httpd:2.4-alpine

# Copy the build output from the 'build-stage' to Apache's default web root directory
# Apache's default DocumentRoot in this image is /usr/local/apache2/htdocs/
COPY --from=build-stage /app/build /usr/local/apache2/htdocs/

# Enable mod_rewrite and AllowOverride for .htaccess processing in the htdocs directory.
# 1. Uncomment LoadModule for rewrite_module (it's usually enabled by default in httpd:2.4-alpine, but this ensures it).
# 2. Change AllowOverride None to AllowOverride All specifically for the /usr/local/apache2/htdocs directory.
RUN sed -i \
    -e 's/^#\(LoadModule rewrite_module modules\/mod_rewrite.so\)/\1/' \
    -e '/<Directory "\/usr\/local\/apache2\/htdocs">/,/<\/Directory>/s/AllowOverride None/AllowOverride All/' \
    /usr/local/apache2/conf/httpd.conf

# Copy the .htaccess file into the htdocs directory
COPY .htaccess /usr/local/apache2/htdocs/.htaccess

# Apache listens on port 80 by default, which is already exposed by the base image.
# EXPOSE 80 (This is inherited from the base httpd:2.4-alpine image, so not strictly necessary to repeat)

# The base httpd:2.4-alpine image already has CMD ["httpd-foreground"],
# so we don't need to specify it again.