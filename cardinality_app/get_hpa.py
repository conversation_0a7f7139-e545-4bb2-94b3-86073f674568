import concurrent.futures
import json
import logging
import os
import requests
import sys
from datetime import datetime, timedelta
from urllib3.exceptions import InsecureRequestWarning


'''
<PERSON><PERSON><PERSON> reads file APP_FILE and fetches the list of applications to analyze HPA metrics from Grafana.
Script requests metric for every region and merge results after that.
You can define default metrics for an application (min and max semicolon separated) which will be used in case the app is not presented in the region (a new app).
APP_FILE Example:
  app-without-defaults
  app-with-defaults;1;4
'''

APP_FILE = 'app_list.txt'
GRAFANA_HOST = "https://grafana.xdr.pan.local:3443"
GRAFANA_TOKEN = os.getenv("GRAFANA_TOKEN")
HEADERS = {"Content-Type": "application/json", "Authorization": f"Bearer {GRAFANA_TOKEN}"}
NOW = datetime.now()

logging.basicConfig(format='%(asctime)s | %(levelname)-5s | %(message).10000s',
                    datefmt='%Y/%m/%d %H:%M:%S', stream=sys.stdout, level=logging.INFO)

requests.packages.urllib3.disable_warnings(category=InsecureRequestWarning)

def get_prometheus_datasources():
    response = requests.get(f"{GRAFANA_HOST}/api/datasources", headers=HEADERS, verify=False)
    return {i['name']:  {"name":i['name'],"id":i['id']} for i in response.json() 
        if "-Prometheus" in i["name"] and i['type'] == 'prometheus' and i['name'] not in ["Shared-Ops-EU-Prometheus","Tenant-Prometheus", "Google-Managed-Prometheus"] and "BACKUP" not in i["name"].upper() and "engines" not in i["name"].lower()}

def run_query(q: str, ds_id: str):
    url = f"{GRAFANA_HOST}/api/datasources/proxy/{ds_id}/api/v1/query"
    q = {"query": q,
         "start": (NOW - timedelta(days=7)).timestamp(),
         "end":  NOW.timestamp(),
         "step": "3600"}
    res = requests.get(url, headers=HEADERS, params=q, verify=False)
    res_json = res.json()
    logging.debug(f"[{ds_id=}] {url=}, {q=}")
    if res_json.get("status") != "success":
        raise Exception(f"Error fetching data from Grafana: {res.status_code} - {res.text}")
    return res_json

def get_min_max(q_min: str, q_max: str, ds_id: str):
    data_max = run_query(q_max, ds_id)["data"]
    data_min = run_query(q_min, ds_id)["data"]

    max_replicas = data_max["result"][0]["value"][1] if len(data_max["result"]) else None
    min_replicas = data_min["result"][0]["value"][1] if len(data_min["result"]) else None

    if min_replicas == 0:
        min_replicas = 1

    return {"min": min_replicas, "max": max_replicas}

def get_app_hpa(app: list[str], ds_id: str):
    q_max = f'max(kube_horizontalpodautoscaler_spec_max_replicas{{horizontalpodautoscaler=~"xdr-st-[0-9]+-{app[0]}-hpa"}} > 0)'
    q_min = f'min(kube_horizontalpodautoscaler_spec_min_replicas{{horizontalpodautoscaler=~"xdr-st-[0-9]+-{app[0]}-hpa"}} > 0)'
    result = get_min_max(q_min, q_max, ds_id)

    if result["min"] is None and result["max"] is None:
        logging.warning(f"[{ds_id=}] HPA is not defined for the app {app[0]}. Checking Deployment replicas.")
        q_max = f'max(kube_deployment_spec_replicas{{deployment=~"xdr-st-[0-9]+-{app[0]}"}} > 0)'
        q_min = f'min(kube_deployment_spec_replicas{{deployment=~"xdr-st-[0-9]+-{app[0]}"}} > 0)'
        result = get_min_max(q_min, q_max, ds_id)

    if result["min"] is None and result["max"] is None:
        logging.warning(f"[{ds_id=}] Deployment is not defined for the app {app[0]}. Checking StatefulSet replicas.")
        q_max = f'max(kube_statefulset_replicas{{statefulset=~".*{app[0]}.*"}} > 0)'
        q_min = f'min(kube_statefulset_replicas{{statefulset=~".*{app[0]}.*"}} > 0)'
        result = get_min_max(q_min, q_max, ds_id)

    if result["min"] is None and result["max"] is None:
        logging.error(f"[{ds_id=}] The {app[0]} app is not presented in the region. Using default HPA values.")
        return {"min": app[1], "max": app[2]} if len(app)>1 else {"min": None, "max": None}

    logging.info(f"[{ds_id=}] HPA for {app[0]}: {result}")
    return result

def get_regional_data(ds: dict):
    logging.warning(f"Getting data for datasource: {ds}")
    ds_id = ds.get('id')
    hpa_data = {}
    with open(APP_FILE, 'r') as f:
        apps = [line.strip() for line in f]
        for app in apps:
            app = app.split(";")
            app_data = get_app_hpa(app, ds_id)
            hpa_data[app[0]] = app_data
    logging.info(f"[{ds_id=}] HPA data collected for {len(hpa_data)} apps.")
    logging.info(f"[{ds_id=}] HPA data: {json.dumps(hpa_data, indent=4)}")
    return hpa_data

    
if __name__ == "__main__":
    data_sources = get_prometheus_datasources()
    logging.info(f"Data sources: {data_sources}")
    hpa_merged = {}

    with concurrent.futures.ThreadPoolExecutor(max_workers=25) as executor:
        res = executor.map(get_regional_data, data_sources.values())
        executor.shutdown()

    # merge data from all data sources
    regions = [r for r in res]
    app_list = regions[0].keys()
    for app in app_list:
        # check the app in every data source result and merge the data
        hpa_min = min([int(region[app]["min"]) for region in regions if region[app]["min"] is not None])
        hpa_max = max([int(region[app]["max"]) for region in regions if region[app]["max"] is not None])
        hpa_merged[app] = {"min": hpa_min, "max": hpa_max}

    logging.info(f"HPA data collected for {len(hpa_merged)} apps.")
    logging.info(f"HPA data: {json.dumps(hpa_merged, indent=4)}")
