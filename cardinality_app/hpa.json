{"agent-api": {"min": 1, "max": 400}, "analytics-alerts-emitter": {"min": 1, "max": 500}, "analytics-decider": {"min": 1, "max": 1}, "analytics-detection-engine": {"min": 1, "max": 3600}, "analytics-detection-engine-external-data": {"min": 1, "max": 800}, "analytics-profiles-orchestrator": {"min": 1, "max": 1}, "analytics-rocksdb": {"min": 1, "max": 6}, "api": {"min": 1, "max": 95}, "apisec-asset-manager": {"min": 1, "max": 5}, "apisec-bff-service": {"min": 1, "max": 3}, "apisec-enricher-service": {"min": 1, "max": 50}, "apisec-grouping-service": {"min": 1, "max": 10}, "apisec-inspection-service": {"min": 1, "max": 10}, "apisec-risk-engine": {"min": 1, "max": 6}, "ca-collection-coordinator": {"min": 1, "max": 3}, "ca-collection-eai-workers": {"min": 1, "max": 3}, "ca-collection-fsi-workers": {"min": 1, "max": 3}, "chrome-app": {"min": 1, "max": 5}, "ciem-api": {"min": 1, "max": 4}, "ciem-epc": {"min": 1, "max": 2}, "ciem-ipc": {"min": 1, "max": 1}, "ciem-rule-scanner": {"min": 1, "max": 2}, "cloud-onboarding-manager": {"min": 1, "max": 1}, "cts": {"min": 1, "max": 60}, "cwp-ads-scan-status-observer": {"min": 2, "max": 4}, "cwp-compliance-publisher": {"min": 1, "max": 10}, "cwp-compliance-uai-scanner": {"min": 1, "max": 10}, "cwp-k8s-api": {"min": 2, "max": 10}, "cwp-k8s-inventory-publisher": {"min": 1, "max": 10}, "cwp-rules-management": {"min": 2, "max": 10}, "cwp-rules-mgmt": {"min": 1, "max": 1}, "cwp-sp-bc-distributor": {"min": 2, "max": 4}, "cwp-sp-bc-fetcher": {"min": 1, "max": 1}, "cwp-sp-workload-orchestration": {"min": 2, "max": 4}, "dms": {"min": 1, "max": 3500}, "dp-asset-ingester": {"min": 1, "max": 100}, "dp-finding-pipeline": {"min": 1, "max": 8}, "egress-forwarding": {"min": 1, "max": 20}, "engine-hub": {"min": 1, "max": 1}, "fetcher": {"min": 1, "max": 2500}, "frontend": {"min": 1, "max": 100}, "ipl-asset-engine": {"min": 1, "max": 50}, "ipl-cronus-node": {"min": 1, "max": 150}, "ipl-metrus-node": {"min": 1, "max": 1}, "issue-fetcher": {"min": 1, "max": 60}, "log-processor": {"min": 1, "max": 250}, "metrics": {"min": 1, "max": 3}, "metrics-aggregator": {"min": 1, "max": 1}, "modules-issue-ingester": {"min": 1, "max": 10}, "notifier": {"min": 1, "max": 20}, "overseer": {"min": 1, "max": 1}, "pb-runner": {"min": 1, "max": 160}, "pb-runner-priority": {"min": 1, "max": 64}, "pb-runner-v2": {"min": 1, "max": 46}, "pipeline": {"min": 1, "max": 2200}, "platform": {"min": 2, "max": 4}, "pz-schema-manager": {"min": 1, "max": 500}, "saas-collection": {"min": 1, "max": 3}, "scortex": {"min": 1, "max": 140}, "scylla": {"min": 1, "max": 80}, "secdo-init": {"min": 1, "max": 1}, "storybuilders": {"min": 1, "max": 150}, "task-processor": {"min": 1, "max": 200}, "xpanse-copilot-api": {"min": 1, "max": 1}, "xql-cdl-engine": {"min": 1, "max": 600}, "xql-engine": {"min": 1, "max": 1500}, "xsoar": {"min": 1, "max": 1}, "xsoar-content": {"min": 1, "max": 1}}